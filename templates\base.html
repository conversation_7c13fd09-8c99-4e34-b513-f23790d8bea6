<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الدراسات العليا{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #34495e;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --white: #ffffff;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: var(--gray-100);
            color: var(--gray-800);
            direction: rtl;
            line-height: 1.6;
            font-size: 16px;
        }
        
        /* Header */
        .main-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
            text-decoration: none;
            color: white;
        }
        
        .logo i {
            margin-left: 10px;
            font-size: 2rem;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: var(--white);
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .user-details {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
            line-height: 1.2;
        }

        .user-type {
            font-size: 0.8rem;
            opacity: 0.8;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }
        
        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg,
                var(--white) 0%,
                rgba(248, 249, 250, 0.95) 50%,
                rgba(236, 240, 241, 0.9) 100%
            );
            width: 280px;
            height: calc(100vh - 80px);
            position: fixed;
            right: 0;
            top: 80px;
            box-shadow: var(--shadow-lg);
            overflow-y: auto;
            transition: var(--transition);
            z-index: 999;
            border-left: 1px solid rgba(52, 152, 219, 0.1);
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                #3498db 0%,
                #9b59b6 25%,
                #2ecc71 50%,
                #f39c12 75%,
                #e74c3c 100%
            );
            z-index: 1;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            text-align: center;
        }

        .sidebar-header h5 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 0;
        }


        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .menu-section {
            margin-bottom: 0.5rem;
        }

        .menu-section-title {
            padding: 1rem 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--gray-600);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            transition: var(--transition);
            border-radius: var(--border-radius);
            margin: 0 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-section-title:hover {
            background: linear-gradient(135deg,
                rgba(52, 152, 219, 0.1) 0%,
                rgba(155, 89, 182, 0.1) 100%
            );
            color: #2c3e50;
            transform: translateX(-3px);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.15);
        }

        .menu-section-title.collapsed .toggle-icon {
            transform: rotate(-90deg);
        }

        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .menu-items {
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .menu-items.collapsed {
            max-height: 0;
        }

        .menu-item {
            display: block;
            padding: 1rem 1.5rem;
            color: var(--gray-700);
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-right: 3px solid transparent;
            font-size: 0.95rem;
            margin: 0 0.5rem;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .menu-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                rgba(52, 152, 219, 0.1),
                rgba(155, 89, 182, 0.1),
                rgba(52, 152, 219, 0.1)
            );
            transition: left 0.5s ease;
            z-index: -1;
        }

        .menu-item:hover::before {
            left: 100%;
        }

        .menu-item:hover {
            background: linear-gradient(135deg,
                rgba(52, 152, 219, 0.15) 0%,
                rgba(155, 89, 182, 0.15) 50%,
                rgba(46, 204, 113, 0.15) 100%
            );
            color: #2c3e50;
            border-right-color: #3498db;
            transform: translateX(-5px) scale(1.02);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
        }

        .menu-item:hover i {
            color: #3498db;
            transform: scale(1.2) rotate(5deg);
        }

        .menu-item.active {
            background: linear-gradient(135deg,
                rgba(52, 152, 219, 0.2) 0%,
                rgba(155, 89, 182, 0.2) 100%
            );
            color: #2c3e50;
            border-right-color: #3498db;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.15);
        }

        .menu-item.active i {
            color: #3498db;
        }

        /* تأثيرات خاصة لكل قسم */
        .menu-item[href="/"] {
            --hover-color: #3498db;
            --hover-bg: rgba(52, 152, 219, 0.15);
        }

        .menu-item[href="/universities"],
        .menu-item[href="/colleges"],
        .menu-item[href="/departments"],
        .menu-item[href="/specializations"],
        .menu-item[href="/academic_years"],
        .menu-item[href="/semesters"],
        .menu-item[href="/subjects"],
        .menu-item[href="/admission_channels"] {
            --hover-color: #9b59b6;
            --hover-bg: rgba(155, 89, 182, 0.15);
        }

        .menu-item[href="/students"],
        .menu-item[href="/faculty"] {
            --hover-color: #2ecc71;
            --hover-bg: rgba(46, 204, 113, 0.15);
        }

        .menu-item[href="/grades_entry"],
        .menu-item[href="/reports"] {
            --hover-color: #f39c12;
            --hover-bg: rgba(243, 156, 18, 0.15);
        }

        .menu-item[href="/users"],
        .menu-item[href="/system_log"],
        .menu-item[href="/backup"] {
            --hover-color: #e74c3c;
            --hover-bg: rgba(231, 76, 60, 0.15);
        }

        /* تطبيق الألوان المخصصة */
        .menu-item:hover {
            background: var(--hover-bg, rgba(52, 152, 219, 0.15)) !important;
            border-right-color: var(--hover-color, #3498db) !important;
        }

        .menu-item:hover i {
            color: var(--hover-color, #3498db) !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .menu-item i {
            width: 28px;
            height: 28px;
            margin-left: 12px;
            text-align: center;
            font-size: 1.1rem;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* تأثيرات خاصة للأيقونات */
        .menu-item:hover i.fa-tachometer-alt {
            animation: pulse 1s infinite;
        }

        .menu-item:hover i.fa-university,
        .menu-item:hover i.fa-building,
        .menu-item:hover i.fa-sitemap,
        .menu-item:hover i.fa-tags {
            animation: bounce 0.6s ease;
        }

        .menu-item:hover i.fa-user-graduate,
        .menu-item:hover i.fa-chalkboard-teacher {
            animation: swing 0.8s ease;
        }

        .menu-item:hover i.fa-chart-line,
        .menu-item:hover i.fa-file-alt {
            animation: tada 0.8s ease;
        }

        .menu-item:hover i.fa-users,
        .menu-item:hover i.fa-history,
        .menu-item:hover i.fa-database {
            animation: rubberBand 0.8s ease;
        }

        /* تحسين القوائم الفرعية والأيقونات */
        .submenu {
            display: none;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 0 0 15px 15px;
            margin-top: 5px;
            padding: 10px 0;
            border-left: 3px solid rgba(52, 152, 219, 0.3);
            backdrop-filter: blur(10px);
        }

        .submenu.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        .submenu-item {
            display: flex;
            align-items: center;
            padding: 12px 20px 12px 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            border-radius: 0 25px 25px 0;
            margin: 2px 10px 2px 0;
            position: relative;
        }

        .submenu-item i {
            width: 24px;
            height: 24px;
            margin-left: 10px;
            text-align: center;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .submenu-item:hover {
            background: rgba(52, 152, 219, 0.15);
            color: #ffffff;
            transform: translateX(-5px);
            padding-right: 25px;
        }

        .submenu-item:hover i {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #3498db;
        }

        .submenu-item.active {
            background: rgba(52, 152, 219, 0.2);
            color: #ffffff;
            font-weight: 600;
        }

        .submenu-item.active i {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        /* تحسين رؤوس القوائم */
        .menu-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            color: rgba(255, 255, 255, 0.9);
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 12px;
            margin: 5px 10px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .menu-header:hover {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
            transform: translateX(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .menu-header i:first-child {
            width: 26px;
            height: 26px;
            font-size: 1.2rem;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .menu-header:hover i:first-child {
            background: linear-gradient(135deg, #5dade2, #3498db);
            transform: scale(1.05) rotate(5deg);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
        }

        .toggle-icon {
            transition: transform 0.3s ease;
            font-size: 0.9rem;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-8px,0); }
            70% { transform: translate3d(0,-4px,0); }
            90% { transform: translate3d(0,-2px,0); }
        }

        @keyframes swing {
            20% { transform: rotate3d(0,0,1,15deg); }
            40% { transform: rotate3d(0,0,1,-10deg); }
            60% { transform: rotate3d(0,0,1,5deg); }
            80% { transform: rotate3d(0,0,1,-5deg); }
            100% { transform: rotate3d(0,0,1,0deg); }
        }

        @keyframes tada {
            0% { transform: scale3d(1, 1, 1); }
            10%, 20% { transform: scale3d(.9, .9, .9) rotate3d(0, 0, 1, -3deg); }
            30%, 50%, 70%, 90% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg); }
            40%, 60%, 80% { transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg); }
            100% { transform: scale3d(1, 1, 1); }
        }

        @keyframes rubberBand {
            0% { transform: scale3d(1, 1, 1); }
            30% { transform: scale3d(1.25, 0.75, 1); }
            40% { transform: scale3d(0.75, 1.25, 1); }
            50% { transform: scale3d(1.15, 0.85, 1); }
            65% { transform: scale3d(.95, 1.05, 1); }
            75% { transform: scale3d(1.05, .95, 1); }
            100% { transform: scale3d(1, 1, 1); }
        }

        /* تأثير التموج للقائمة */
        .menu-ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(52, 152, 219, 0.3);
            transform: scale(0);
            animation: menu-ripple-animation 0.6s linear;
            pointer-events: none;
            z-index: 0;
        }

        @keyframes menu-ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* تحسين الشريط العلوي الملون */
        .sidebar::before {
            animation: rainbow-flow 8s ease-in-out infinite;
        }

        @keyframes rainbow-flow {
            0%, 100% {
                background: linear-gradient(90deg,
                    #3498db 0%,
                    #9b59b6 25%,
                    #2ecc71 50%,
                    #f39c12 75%,
                    #e74c3c 100%
                );
            }
            25% {
                background: linear-gradient(90deg,
                    #e74c3c 0%,
                    #3498db 25%,
                    #9b59b6 50%,
                    #2ecc71 75%,
                    #f39c12 100%
                );
            }
            50% {
                background: linear-gradient(90deg,
                    #f39c12 0%,
                    #e74c3c 25%,
                    #3498db 50%,
                    #9b59b6 75%,
                    #2ecc71 100%
                );
            }
            75% {
                background: linear-gradient(90deg,
                    #2ecc71 0%,
                    #f39c12 25%,
                    #e74c3c 50%,
                    #3498db 75%,
                    #9b59b6 100%
                );
            }
        }

        /* Global Notification Styles */
        .success-icon-container {
            position: relative;
            display: inline-block;
        }

        .success-animation {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            border: 3px solid transparent;
            border-top: 3px solid #27ae60;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .global-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            z-index: 9999;
            transition: width 0.3s ease;
            display: none;
        }

        .global-progress.show {
            display: block;
            animation: progress-pulse 1.5s ease-in-out infinite;
        }

        @keyframes progress-pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        /* Main Content */
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: calc(100vh - 80px);
            transition: var(--transition);
        }
        
        /* Page Header */
        .page-header {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--shadow-sm);
            border-left: 4px solid var(--accent-color);
        }
        
        .page-title {
            font-size: 2.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title i {
            font-size: 2rem;
            color: var(--accent-color);
        }

        .page-subtitle {
            color: var(--gray-600);
            font-size: 1.2rem;
            font-weight: 400;
        }
        
        /* Cards */
        .card {
            background: var(--white);
            border: none;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(155, 89, 182, 0.05));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-5px) scale(1.02);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card-header {
            background: var(--gray-100);
            border-bottom: 1px solid var(--gray-200);
            padding: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            font-size: 1.1rem;
            position: relative;
            z-index: 1;
        }

        .card-body {
            padding: 2rem;
            position: relative;
            z-index: 1;
            font-size: 1rem;
        }
        
        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 0.75rem 1.5rem;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background-color: var(--accent-color);
            color: white;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .btn-success:hover {
            background-color: #229954;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
            color: white;
        }

        .btn-warning {
            background-color: var(--warning-color);
            color: white;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .btn-warning:hover {
            background-color: #e67e22;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .btn-danger:hover {
            background-color: #c0392b;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
            color: white;
        }

        .btn-info {
            background-color: var(--info-color);
            color: white;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }

        .btn-info:hover {
            background-color: #138496;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
            color: white;
        }

        .btn:active {
            transform: translateY(-1px) scale(1.02);
        }
        
        /* Forms */
        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 2px solid var(--gray-300);
            padding: 1rem;
            transition: var(--transition);
            font-size: 1rem;
            background-color: var(--white);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.3rem rgba(52, 152, 219, 0.15);
            transform: translateY(-2px);
        }

        .form-label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 1.1rem;
            margin-bottom: 0.75rem;
        }
        
        /* Tables */
        .table {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            font-size: 1rem;
        }

        .table th {
            background-color: var(--gray-100);
            border-bottom: 2px solid var(--gray-200);
            font-weight: 600;
            color: var(--primary-color);
            padding: 1.25rem;
            font-size: 1.1rem;
        }

        .table td {
            border-bottom: 1px solid var(--gray-200);
            vertical-align: middle;
            padding: 1.25rem;
            font-size: 1rem;
        }

        .table tbody tr {
            transition: all 0.3s ease;
        }

        .table tbody tr:hover {
            background-color: var(--gray-50);
            transform: scale(1.01);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="container-fluid">
            <div class="header-content">
                <a href="/" class="logo">
                    <i class="fas fa-graduation-cap"></i>
                    نظام إدارة الدراسات العليا
                </a>

                <div class="user-menu">
                    <div class="user-info">
                        <div class="user-avatar">
                            {{ session.full_name[0].upper() if session.full_name else (session.username[0].upper() if session.username else 'U') }}
                        </div>
                        <div class="user-details">
                            <span class="user-name">{{ session.full_name or session.username or 'مستخدم' }}</span>
                            <small class="user-type">{{ session.user_type or 'مستخدم' }}</small>
                        </div>
                    </div>
                    <a href="/logout" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <h5>القائمة الرئيسية</h5>
        </div>



        <nav class="sidebar-menu">
            <div class="menu-section">
                <div class="menu-section-title" onclick="toggleMenuSection(this)">
                    لوحة التحكم
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items">
                    <a href="/" class="menu-item">
                        <i class="fas fa-tachometer-alt"></i>
                        الرئيسية
                    </a>
                </div>
            </div>

            <div class="menu-section">
                <div class="menu-section-title" onclick="toggleMenuSection(this)">
                    البيانات الأساسية
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items">
                    <a href="/universities" class="menu-item">
                        <i class="fas fa-university"></i>
                        الجامعات
                    </a>
                    <a href="/colleges" class="menu-item">
                        <i class="fas fa-building"></i>
                        الكليات
                    </a>
                    <a href="/departments" class="menu-item">
                        <i class="fas fa-sitemap"></i>
                        الأقسام
                    </a>
                    <a href="/specializations" class="menu-item">
                        <i class="fas fa-tags"></i>
                        التخصصات
                    </a>
                    <a href="/academic_programs" class="menu-item">
                        <i class="fas fa-graduation-cap"></i>
                        البرامج الأكاديمية
                    </a>
                    <a href="/academic_years" class="menu-item">
                        <i class="fas fa-calendar-alt"></i>
                        الأعوام الدراسية
                    </a>
                    <a href="/semesters" class="menu-item">
                        <i class="fas fa-calendar-week"></i>
                        الفصول الدراسية
                    </a>
                    <a href="/subjects" class="menu-item">
                        <i class="fas fa-book-open"></i>
                        المواد الدراسية
                    </a>
                    <a href="/admission_channels" class="menu-item">
                        <i class="fas fa-door-open"></i>
                        قنوات القبول
                    </a>
                </div>
            </div>

            <div class="menu-section">
                <div class="menu-section-title" onclick="toggleMenuSection(this)">
                    أعضاء الهيئة التدريسية والطلبة
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items">
                    <a href="/faculty" class="menu-item">
                        <i class="fas fa-chalkboard-teacher"></i>
                        أعضاء هيئة التدريس
                    </a>
                    <a href="/students" class="menu-item">
                        <i class="fas fa-user-graduate"></i>
                        الطلبة
                    </a>
                </div>
            </div>

            <div class="menu-section">
                <div class="menu-section-title" onclick="toggleMenuSection(this)">
                    التقييم والنتائج
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items">
                    <a href="/grades_entry" class="menu-item">
                        <i class="fas fa-edit"></i>
                        إدخال الدرجات
                    </a>
                    <a href="/semester_reports" class="menu-item">
                        <i class="fas fa-chart-line"></i>
                        نتيجة الفصل الدراسي الأول
                    </a>
                    <a href="/reports" class="menu-item">
                        <i class="fas fa-file-alt"></i>
                        التقارير
                    </a>
                </div>
            </div>

            <div class="menu-section">
                <div class="menu-section-title" onclick="toggleMenuSection(this)">
                    إدارة المستخدمين
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items">
                    <a href="/users" class="menu-item">
                        <i class="fas fa-users"></i>
                        المستخدمين
                    </a>
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div class="menu-section">
                <div class="menu-section-title" onclick="toggleMenuSection(this)">
                    إعدادات النظام
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </div>
                <div class="menu-items">
                    <a href="/system_log" class="menu-item">
                        <i class="fas fa-history"></i>
                        سجل النظام
                    </a>
                    <a href="/backup" class="menu-item">
                        <i class="fas fa-shield-alt"></i>
                        النسخ الاحتياطي والاستعادة
                    </a>
                    <a href="/settings" class="menu-item">
                        <i class="fas fa-database"></i>
                        إعدادات الخادم
                    </a>
                </div>
            </div>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Global Notification Modals -->
    <!-- Success Modal -->
    <div class="modal fade" id="globalSuccessModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-body text-center p-5">
                    <div class="mb-4">
                        <div class="success-icon-container">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                            <div class="success-animation"></div>
                        </div>
                    </div>
                    <h4 class="text-success mb-3" id="globalSuccessTitle">تم بنجاح!</h4>
                    <p class="text-muted mb-4" id="globalSuccessMessage">تمت العملية بنجاح</p>
                    <button type="button" class="btn btn-success px-4" data-bs-dismiss="modal">
                        <i class="fas fa-thumbs-up me-2"></i>
                        ممتاز
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal fade" id="globalErrorModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-danger mb-3" id="globalErrorTitle">حدث خطأ!</h4>
                    <p class="text-muted mb-4" id="globalErrorMessage">حدث خطأ أثناء العملية</p>
                    <button type="button" class="btn btn-danger px-4" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        إغلاق
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Warning Modal -->
    <div class="modal fade" id="globalWarningModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-circle text-warning" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-warning mb-3" id="globalWarningTitle">تحذير!</h4>
                    <p class="text-muted mb-4" id="globalWarningMessage">يرجى الانتباه</p>
                    <button type="button" class="btn btn-warning px-4" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>
                        فهمت
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div class="modal fade" id="globalInfoModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 shadow-lg">
                <div class="modal-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-info-circle text-info" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-info mb-3" id="globalInfoTitle">معلومة!</h4>
                    <p class="text-muted mb-4" id="globalInfoMessage">معلومة مفيدة</p>
                    <button type="button" class="btn btn-info px-4" data-bs-dismiss="modal">
                        <i class="fas fa-check me-2"></i>
                        حسناً
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div id="globalProgressBar" class="global-progress"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Styles Block -->
    {% block styles %}{% endblock %}

    <script>
        // Global Notification System
        function showAlert(message, type = 'success', title = null) {
            const types = {
                'success': {
                    modal: 'globalSuccessModal',
                    titleElement: 'globalSuccessTitle',
                    messageElement: 'globalSuccessMessage',
                    defaultTitle: 'تم بنجاح!'
                },
                'error': {
                    modal: 'globalErrorModal',
                    titleElement: 'globalErrorTitle',
                    messageElement: 'globalErrorMessage',
                    defaultTitle: 'حدث خطأ!'
                },
                'warning': {
                    modal: 'globalWarningModal',
                    titleElement: 'globalWarningTitle',
                    messageElement: 'globalWarningMessage',
                    defaultTitle: 'تحذير!'
                },
                'info': {
                    modal: 'globalInfoModal',
                    titleElement: 'globalInfoTitle',
                    messageElement: 'globalInfoMessage',
                    defaultTitle: 'معلومة!'
                }
            };

            const config = types[type] || types['info'];

            // تحديث النص
            document.getElementById(config.titleElement).textContent = title || config.defaultTitle;
            document.getElementById(config.messageElement).textContent = message;

            // إظهار النافذة
            const modal = new bootstrap.Modal(document.getElementById(config.modal));
            modal.show();

            // إخفاء تلقائي بعد 3 ثوان
            setTimeout(() => {
                modal.hide();
            }, 3000);
        }

        // دوال مخصصة لكل نوع
        function showSuccessModal(message, title = 'تم الحفظ بنجاح!') {
            showAlert(message, 'success', title);
        }

        function showErrorModal(message, title = 'حدث خطأ!') {
            showAlert(message, 'error', title);
        }

        function showWarningModal(message, title = 'تحذير!') {
            showAlert(message, 'warning', title);
        }

        function showInfoModal(message, title = 'معلومة!') {
            showAlert(message, 'info', title);
        }

        // شريط التقدم العام
        function showProgress(percentage = 0) {
            const progressBar = document.getElementById('globalProgressBar');
            progressBar.style.width = percentage + '%';
            progressBar.classList.add('show');
        }

        function hideProgress() {
            const progressBar = document.getElementById('globalProgressBar');
            progressBar.classList.remove('show');
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 300);
        }

        // دوال للعمليات الشائعة
        function showSaveSuccess(message = 'تم حفظ البيانات بنجاح') {
            showSuccessModal(message, 'تم الحفظ بنجاح!');
        }

        function showUpdateSuccess(message = 'تم تحديث البيانات بنجاح') {
            showSuccessModal(message, 'تم التحديث بنجاح!');
        }

        function showDeleteSuccess(message = 'تم حذف البيانات بنجاح') {
            showSuccessModal(message, 'تم الحذف بنجاح!');
        }

        function showSaveError(message = 'فشل في حفظ البيانات') {
            showErrorModal(message, 'خطأ في الحفظ!');
        }

        function showUpdateError(message = 'فشل في تحديث البيانات') {
            showErrorModal(message, 'خطأ في التحديث!');
        }

        function showDeleteError(message = 'فشل في حذف البيانات') {
            showErrorModal(message, 'خطأ في الحذف!');
        }

        // تفعيل الرابط النشط في القائمة
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.menu-item');

            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');
                    // فتح القسم الذي يحتوي على الرابط النشط
                    const menuSection = item.closest('.menu-section');
                    if (menuSection) {
                        const menuItems = menuSection.querySelector('.menu-items');
                        const sectionTitle = menuSection.querySelector('.menu-section-title');
                        if (menuItems && sectionTitle) {
                            menuItems.style.maxHeight = menuItems.scrollHeight + 'px';
                            sectionTitle.classList.remove('collapsed');
                        }
                    }
                }
            });

            // تعيين الحد الأقصى للارتفاع للقوائم المفتوحة
            const menuItemsElements = document.querySelectorAll('.menu-items');
            menuItemsElements.forEach(items => {
                if (!items.classList.contains('collapsed')) {
                    items.style.maxHeight = items.scrollHeight + 'px';
                }
            });
        });

        // تبديل حالة أقسام القائمة
        function toggleMenuSection(titleElement) {
            const menuSection = titleElement.closest('.menu-section');
            const menuItems = menuSection.querySelector('.menu-items');
            const toggleIcon = titleElement.querySelector('.toggle-icon');

            if (menuItems.style.maxHeight && menuItems.style.maxHeight !== '0px') {
                // إغلاق القسم
                menuItems.style.maxHeight = '0px';
                titleElement.classList.add('collapsed');
            } else {
                // فتح القسم
                menuItems.style.maxHeight = menuItems.scrollHeight + 'px';
                titleElement.classList.remove('collapsed');
            }
        }

        // تأثير التموج للقائمة الجانبية
        function createRippleEffect(element, event) {
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('menu-ripple');

            element.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // إضافة تأثير التموج لعناصر القائمة
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');

            menuItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    createRippleEffect(this, e);
                });
            });
        });

        // إظهار/إخفاء الشريط الجانبي في الشاشات الصغيرة
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }

        // وظيفة لتبديل القوائم الفرعية
        function toggleSubmenu(submenuId) {
            const submenu = document.getElementById(submenuId + '-submenu');
            const icon = document.getElementById(submenuId + '-icon');

            if (submenu) {
                if (submenu.classList.contains('show')) {
                    submenu.classList.remove('show');
                    if (icon) icon.classList.remove('rotated');
                } else {
                    // إخفاء جميع القوائم الفرعية الأخرى
                    document.querySelectorAll('.submenu').forEach(menu => {
                        menu.classList.remove('show');
                    });
                    document.querySelectorAll('.toggle-icon').forEach(ic => {
                        ic.classList.remove('rotated');
                    });

                    // إظهار القائمة المطلوبة
                    submenu.classList.add('show');
                    if (icon) icon.classList.add('rotated');
                }
            }
        }

        // تأثيرات تفاعلية للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');

            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.03)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تفعيل الرابط النشط
            const currentPath = window.location.pathname;
            const menuItems = document.querySelectorAll('.menu-item, .submenu-item');

            menuItems.forEach(item => {
                if (item.getAttribute('href') === currentPath) {
                    item.classList.add('active');

                    // إذا كان العنصر في قائمة فرعية، افتح القائمة الفرعية
                    if (item.classList.contains('submenu-item')) {
                        const submenu = item.closest('.submenu');
                        if (submenu) {
                            submenu.classList.add('show');
                            const submenuId = submenu.id.replace('-submenu', '');
                            const icon = document.getElementById(submenuId + '-icon');
                            if (icon) icon.classList.add('rotated');
                        }
                    }
                }
            });
        });

        // دالة عامة لعرض رسائل التنبيه للمستخدمين العاديين
        function showAccessDeniedAlert() {
            const alertHTML = `
                <div class="alert alert-warning alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تنبيه!</strong> ليس لديك صلاحية للوصول إلى هذه الصفحة.
                    هذه الصفحة مخصصة لمديري النظام فقط.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.body.insertAdjacentHTML('afterbegin', alertHTML);

            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = document.querySelector('.alert-warning');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // فحص صلاحيات المستخدم عند النقر على الروابط
        document.addEventListener('DOMContentLoaded', function() {
            const userType = '{{ session.user_type if session.user_type else "" }}';

            // قائمة الصفحات المحظورة على المستخدمين العاديين
            const adminOnlyPages = ['/users', '/settings', '/backup', '/system_log'];

            // إضافة مستمع للنقر على جميع الروابط
            document.addEventListener('click', function(e) {
                const link = e.target.closest('a');
                if (link && userType === 'مستخدم عادي') {
                    const href = link.getAttribute('href');

                    if (adminOnlyPages.includes(href)) {
                        e.preventDefault();
                        showAccessDeniedAlert();
                        return false;
                    }
                }
            });
        });
    </script>

    <!-- Custom Scripts Block -->
    {% block scripts %}{% endblock %}
</body>
</html>
