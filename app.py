from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, Response, make_response
import pyodbc
import os
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import base64

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this to a random secret key

def get_time_ago(operation_date):
    """حساب الوقت المنقضي منذ العملية"""
    if not operation_date:
        return 'غير محدد'

    now = datetime.now()
    diff = now - operation_date

    if diff.days > 0:
        return f'منذ {diff.days} يوم'
    elif diff.seconds >= 3600:
        hours = diff.seconds // 3600
        return f'منذ {hours} ساعة'
    elif diff.seconds >= 60:
        minutes = diff.seconds // 60
        return f'منذ {minutes} دقيقة'
    else:
        return 'منذ لحظات'

# Configuration
UPLOAD_FOLDER = 'static/uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Create upload directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Database connection
from database_config import db_config, get_db_connection, test_connection

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    # فحص الاتصال بقاعدة البيانات أولاً
    if not test_connection():
        return redirect(url_for('database_setup'))

    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            cursor.execute("SELECT user_id, username, full_name, password_hash, user_type, status FROM users WHERE username = ? AND status = 'نشط'", (username,))
            user = cursor.fetchone()

            if user and check_password_hash(user.password_hash, password):
                session['user_id'] = user.user_id
                session['username'] = user.username
                session['full_name'] = user.full_name
                session['user_type'] = user.user_type
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('index'))
            else:
                flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

            conn.close()
        except Exception as e:
            flash(f'خطأ في الاتصال بقاعدة البيانات: {str(e)}', 'error')
            return redirect(url_for('database_setup'))

    # جلب قائمة المستخدمين النشطين
    users = []
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT username, full_name FROM users WHERE status = 'نشط' ORDER BY full_name")
        users = [{'username': row[0], 'full_name': row[1]} for row in cursor.fetchall()]
        conn.close()
    except Exception as e:
        print(f"خطأ في جلب المستخدمين: {str(e)}")

    return render_template('login.html', users=users)

@app.route('/register', methods=['POST'])
def register():
    """تسجيل مستخدم جديد"""
    try:
        username = request.form['username']
        full_name = request.form['full_name']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        role = request.form['role']

        # التحقق من تطابق كلمات المرور
        if password != confirm_password:
            flash('كلمات المرور غير متطابقة', 'error')
            return redirect(url_for('login'))

        # التحقق من طول كلمة المرور
        if len(password) < 6:
            flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
            return redirect(url_for('login'))

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود اسم المستخدم مسبقاً
        cursor.execute("SELECT username FROM users WHERE username = ?", (username,))
        if cursor.fetchone():
            flash('اسم المستخدم موجود مسبقاً', 'error')
            conn.close()
            return redirect(url_for('login'))

        # تشفير كلمة المرور
        password_hash = generate_password_hash(password)

        # تحديد نوع المستخدم
        user_type = 'مدير النظام' if role == 'admin' else 'مستخدم عادي'

        # إدراج المستخدم الجديد
        cursor.execute("""
            INSERT INTO users (username, full_name, password_hash, user_type, status, created_date)
            VALUES (?, ?, ?, ?, 'نشط', GETDATE())
        """, (username, full_name, password_hash, user_type))

        conn.commit()
        conn.close()

        flash('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success')
        return redirect(url_for('login'))

    except Exception as e:
        flash(f'خطأ في إنشاء الحساب: {str(e)}', 'error')
        return redirect(url_for('login'))

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # إحصائيات النظام من قاعدة البيانات
        stats = {}

        # عدد الطلبة
        cursor.execute("SELECT COUNT(*) FROM students")
        stats['total_students'] = cursor.fetchone()[0]

        # عدد أعضاء هيئة التدريس
        cursor.execute("SELECT COUNT(*) FROM faculty")
        stats['total_faculty'] = cursor.fetchone()[0]

        # عدد البرامج الأكاديمية
        cursor.execute("SELECT COUNT(*) FROM academic_programs")
        stats['total_programs'] = cursor.fetchone()[0]

        # عدد المواد الدراسية
        cursor.execute("SELECT COUNT(*) FROM subjects")
        stats['total_courses'] = cursor.fetchone()[0]

        # عدد الجامعات
        cursor.execute("SELECT COUNT(*) FROM universities")
        stats['total_universities'] = cursor.fetchone()[0]

        # عدد الكليات
        cursor.execute("SELECT COUNT(*) FROM colleges")
        stats['total_colleges'] = cursor.fetchone()[0]

        # عدد الأقسام
        cursor.execute("SELECT COUNT(*) FROM departments")
        stats['total_departments'] = cursor.fetchone()[0]

        # عدد التخصصات
        cursor.execute("SELECT COUNT(*) FROM specializations")
        stats['total_specializations'] = cursor.fetchone()[0]

        # إحصائيات سريعة إضافية
        # الطلبة النشطون (المستمرون)
        cursor.execute("SELECT COUNT(*) FROM students WHERE status IN (N'مستمر في السنة التحضيرية', N'مستمر في السنة البحثية')")
        stats['active_students'] = cursor.fetchone()[0]

        # المتخرجون
        cursor.execute("SELECT COUNT(*) FROM students WHERE status = N'خريج'")
        stats['graduated_students'] = cursor.fetchone()[0]

        # الطلبة الجدد (المضافون خلال آخر 30 يوم)
        cursor.execute("SELECT COUNT(*) FROM students WHERE created_date >= DATEADD(day, -30, GETDATE())")
        stats['new_students'] = cursor.fetchone()[0]

        # معدل النجاح (الطلبة الذين لديهم درجات >= 60)
        cursor.execute("""
            SELECT
                CASE
                    WHEN COUNT(*) = 0 THEN 0
                    ELSE CAST(COUNT(CASE WHEN total_grade >= 60 THEN 1 END) * 100.0 / COUNT(*) AS INT)
                END as success_rate
            FROM grades
            WHERE total_grade IS NOT NULL
        """)
        result = cursor.fetchone()
        stats['success_rate'] = result[0] if result else 0

        # الأنشطة الحديثة من سجل النظام
        cursor.execute("""
            SELECT TOP 5 operation_name, operation_type, target_object, username, operation_date
            FROM system_log
            ORDER BY operation_date DESC
        """)

        recent_activities = []
        for row in cursor.fetchall():
            activity = {
                'operation_name': row.operation_name,
                'operation_type': row.operation_type,
                'target_object': row.target_object,
                'username': row.username,
                'operation_date': row.operation_date,
                'time_ago': get_time_ago(row.operation_date) if row.operation_date else 'غير محدد'
            }
            recent_activities.append(activity)

        conn.close()

    except Exception as e:
        print(f"خطأ في تحميل الإحصائيات: {str(e)}")
        # إحصائيات افتراضية في حالة الخطأ
        stats = {
            'total_students': 0,
            'total_faculty': 0,
            'total_programs': 0,
            'total_courses': 0,
            'total_universities': 0,
            'total_colleges': 0,
            'total_departments': 0,
            'total_specializations': 0,
            'active_students': 0,
            'graduated_students': 0,
            'new_students': 0,
            'success_rate': 0
        }
        recent_activities = []

    return render_template('dashboard.html', stats=stats, recent_activities=recent_activities, user=session)

# Basic Data Management Routes
@app.route('/universities')
def universities():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('universities.html')

@app.route('/api/universities', methods=['GET'])
def get_universities():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT university_id, name_ar, name_en, address, logo_filename, created_date
            FROM universities
            ORDER BY created_date DESC
        """)

        universities = []
        for row in cursor.fetchall():
            universities.append({
                'university_id': row.university_id,
                'name_ar': row.name_ar,
                'name_en': row.name_en,
                'address': row.address,
                'logo_filename': row.logo_filename,
                'has_logo': bool(row.logo_filename),
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None
            })

        conn.close()
        return jsonify(universities)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/universities', methods=['POST'])
def add_university():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        print("=== بدء إضافة جامعة ===")
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')

        print(f"البيانات المستلمة: name_ar={name_ar}, name_en={name_en}, address={address}")
        print(f"الملفات المرفقة: {list(request.files.keys())}")

        if not name_ar:
            return jsonify({'error': 'اسم الجامعة بالعربية مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # معالجة الصورة إذا تم رفعها
        logo_data = None
        logo_filename = None
        logo_content_type = None

        if 'logo' in request.files:
            logo_file = request.files['logo']
            print(f"ملف الشعار: {logo_file.filename}")
            if logo_file and logo_file.filename != '' and allowed_file(logo_file.filename):
                logo_data = logo_file.read()
                logo_filename = secure_filename(logo_file.filename)
                logo_content_type = logo_file.content_type
                print(f"تم قراءة الصورة: {len(logo_data)} بايت")
            else:
                print("الملف غير صالح أو فارغ")
        else:
            print("لا يوجد ملف شعار")

        print("تنفيذ استعلام الإدراج...")
        cursor.execute("""
            INSERT INTO universities (name_ar, name_en, address, logo_data, logo_filename, logo_content_type, created_date)
            VALUES (?, ?, ?, ?, ?, ?, GETDATE())
        """, (name_ar, name_en, address, logo_data, logo_filename, logo_content_type))

        conn.commit()
        print("تم الحفظ في قاعدة البيانات")

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة جامعة', 'INSERT', f"جامعة: {name_ar}", session.get('username')))

        conn.commit()
        conn.close()
        print("=== انتهاء إضافة جامعة بنجاح ===")

        return jsonify({'message': 'تم إضافة الجامعة بنجاح'}), 201

    except Exception as e:
        print(f"خطأ في إضافة الجامعة: {str(e)}")
        return jsonify({'error': f'خطأ في إضافة الجامعة: {str(e)}'}), 500

@app.route('/api/universities/<int:university_id>', methods=['PUT'])
def update_university(university_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')

        if not name_ar:
            return jsonify({'error': 'اسم الجامعة بالعربية مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # معالجة الصورة إذا تم رفعها
        if 'logo' in request.files:
            logo_file = request.files['logo']
            if logo_file and logo_file.filename != '' and allowed_file(logo_file.filename):
                logo_data = logo_file.read()
                logo_filename = secure_filename(logo_file.filename)
                logo_content_type = logo_file.content_type

                cursor.execute("""
                    UPDATE universities
                    SET name_ar = ?, name_en = ?, address = ?, logo_data = ?, logo_filename = ?, logo_content_type = ?, updated_date = GETDATE()
                    WHERE university_id = ?
                """, (name_ar, name_en, address, logo_data, logo_filename, logo_content_type, university_id))
            else:
                cursor.execute("""
                    UPDATE universities
                    SET name_ar = ?, name_en = ?, address = ?, updated_date = GETDATE()
                    WHERE university_id = ?
                """, (name_ar, name_en, address, university_id))
        else:
            cursor.execute("""
                UPDATE universities
                SET name_ar = ?, name_en = ?, address = ?, updated_date = GETDATE()
                WHERE university_id = ?
            """, (name_ar, name_en, address, university_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'الجامعة غير موجودة'}), 404

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تعديل جامعة', 'UPDATE', f"جامعة: {name_ar}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تعديل الجامعة بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تعديل الجامعة: {str(e)}'}), 500

@app.route('/api/universities/<int:university_id>', methods=['DELETE'])
def delete_university(university_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود كليات مرتبطة بالجامعة
        cursor.execute("SELECT COUNT(*) FROM colleges WHERE university_id = ?", (university_id,))
        college_count = cursor.fetchone()[0]

        if college_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف الجامعة لوجود {college_count} كلية مرتبطة بها'}), 400

        # الحصول على اسم الجامعة قبل الحذف
        cursor.execute("SELECT name_ar FROM universities WHERE university_id = ?", (university_id,))
        university = cursor.fetchone()

        if not university:
            conn.close()
            return jsonify({'error': 'الجامعة غير موجودة'}), 404

        university_name = university.name_ar

        cursor.execute("DELETE FROM universities WHERE university_id = ?", (university_id,))

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف جامعة', 'DELETE', f"جامعة: {university_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف الجامعة بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف الجامعة: {str(e)}'}), 500

@app.route('/api/universities/<int:university_id>/logo')
def get_university_logo(university_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT logo_data, logo_content_type, logo_filename
            FROM universities
            WHERE university_id = ? AND logo_data IS NOT NULL
        """, (university_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return Response(
                result.logo_data,
                mimetype=result.logo_content_type or 'image/jpeg',
                headers={'Content-Disposition': f'inline; filename="{result.logo_filename}"'}
            )
        else:
            return jsonify({'error': 'الشعار غير موجود'}), 404

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل الشعار: {str(e)}'}), 500

@app.route('/colleges')
def colleges():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('colleges.html')

@app.route('/api/colleges', methods=['GET'])
def get_colleges():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT c.college_id, c.university_id, c.name_ar, c.name_en, c.address,
                   c.logo_filename, c.created_date, u.name_ar as university_name
            FROM colleges c
            LEFT JOIN universities u ON c.university_id = u.university_id
            ORDER BY c.created_date DESC
        """)

        colleges = []
        for row in cursor.fetchall():
            colleges.append({
                'college_id': row.college_id,
                'university_id': row.university_id,
                'name_ar': row.name_ar,
                'name_en': row.name_en,
                'address': row.address,
                'logo_filename': row.logo_filename,
                'has_logo': bool(row.logo_filename),
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None,
                'university_name': row.university_name
            })

        conn.close()
        return jsonify(colleges)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/colleges', methods=['POST'])
def add_college():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        print("=== بدء إضافة كلية ===")
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')
        university_id = request.form.get('university_id')

        print(f"البيانات المستلمة: name_ar={name_ar}, name_en={name_en}, address={address}, university_id={university_id}")
        print(f"الملفات المرفقة: {list(request.files.keys())}")

        if not name_ar:
            return jsonify({'error': 'اسم الكلية بالعربية مطلوب'}), 400

        if not university_id:
            return jsonify({'error': 'الجامعة مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # معالجة الصورة إذا تم رفعها
        logo_data = None
        logo_filename = None
        logo_content_type = None

        if 'logo' in request.files:
            logo_file = request.files['logo']
            print(f"ملف الشعار: {logo_file.filename}")
            if logo_file and logo_file.filename != '' and allowed_file(logo_file.filename):
                logo_data = logo_file.read()
                logo_filename = secure_filename(logo_file.filename)
                logo_content_type = logo_file.content_type
                print(f"تم قراءة الصورة: {len(logo_data)} بايت")
            else:
                print("الملف غير صالح أو فارغ")
        else:
            print("لا يوجد ملف شعار")

        print("تنفيذ استعلام الإدراج...")
        cursor.execute("""
            INSERT INTO colleges (university_id, name_ar, name_en, address, logo_data, logo_filename, logo_content_type, created_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, GETDATE())
        """, (university_id, name_ar, name_en, address, logo_data, logo_filename, logo_content_type))

        conn.commit()
        print("تم الحفظ في قاعدة البيانات")

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة كلية', 'INSERT', f"كلية: {name_ar}", session.get('username')))

        conn.commit()
        conn.close()
        print("=== انتهاء إضافة كلية بنجاح ===")

        return jsonify({'message': 'تم إضافة الكلية بنجاح'}), 201

    except Exception as e:
        print(f"خطأ في إضافة الكلية: {str(e)}")
        return jsonify({'error': f'خطأ في إضافة الكلية: {str(e)}'}), 500

@app.route('/api/colleges/<int:college_id>', methods=['PUT'])
def update_college(college_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        name_ar = request.form.get('name_ar')
        name_en = request.form.get('name_en')
        address = request.form.get('address')
        university_id = request.form.get('university_id')

        if not name_ar:
            return jsonify({'error': 'اسم الكلية بالعربية مطلوب'}), 400

        if not university_id:
            return jsonify({'error': 'الجامعة مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # معالجة الصورة إذا تم رفعها
        if 'logo' in request.files:
            logo_file = request.files['logo']
            if logo_file and logo_file.filename != '' and allowed_file(logo_file.filename):
                logo_data = logo_file.read()
                logo_filename = secure_filename(logo_file.filename)
                logo_content_type = logo_file.content_type

                cursor.execute("""
                    UPDATE colleges
                    SET university_id = ?, name_ar = ?, name_en = ?, address = ?,
                        logo_data = ?, logo_filename = ?, logo_content_type = ?, updated_date = GETDATE()
                    WHERE college_id = ?
                """, (university_id, name_ar, name_en, address, logo_data, logo_filename, logo_content_type, college_id))
            else:
                cursor.execute("""
                    UPDATE colleges
                    SET university_id = ?, name_ar = ?, name_en = ?, address = ?, updated_date = GETDATE()
                    WHERE college_id = ?
                """, (university_id, name_ar, name_en, address, college_id))
        else:
            cursor.execute("""
                UPDATE colleges
                SET university_id = ?, name_ar = ?, name_en = ?, address = ?, updated_date = GETDATE()
                WHERE college_id = ?
            """, (university_id, name_ar, name_en, address, college_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'الكلية غير موجودة'}), 404

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تعديل كلية', 'UPDATE', f"كلية: {name_ar}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تعديل الكلية بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تعديل الكلية: {str(e)}'}), 500

@app.route('/api/colleges/<int:college_id>', methods=['DELETE'])
def delete_college(college_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود أقسام مرتبطة بالكلية
        cursor.execute("SELECT COUNT(*) FROM departments WHERE college_id = ?", (college_id,))
        department_count = cursor.fetchone()[0]

        if department_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف الكلية لوجود {department_count} قسم مرتبط بها'}), 400

        # الحصول على اسم الكلية قبل الحذف
        cursor.execute("SELECT name_ar FROM colleges WHERE college_id = ?", (college_id,))
        college = cursor.fetchone()

        if not college:
            conn.close()
            return jsonify({'error': 'الكلية غير موجودة'}), 404

        college_name = college.name_ar

        cursor.execute("DELETE FROM colleges WHERE college_id = ?", (college_id,))

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف كلية', 'DELETE', f"كلية: {college_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف الكلية بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف الكلية: {str(e)}'}), 500

@app.route('/api/colleges/<int:college_id>/logo')
def get_college_logo(college_id):
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT logo_data, logo_content_type, logo_filename
            FROM colleges
            WHERE college_id = ? AND logo_data IS NOT NULL
        """, (college_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return Response(
                result.logo_data,
                mimetype=result.logo_content_type or 'image/jpeg',
                headers={'Content-Disposition': f'inline; filename="{result.logo_filename}"'}
            )
        else:
            return jsonify({'error': 'الشعار غير موجود'}), 404

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل الشعار: {str(e)}'}), 500

# API للحصول على قوائم الاختيار
@app.route('/api/universities/list', methods=['GET'])
def get_universities_list():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT university_id, name_ar FROM universities ORDER BY name_ar")

        universities = []
        for row in cursor.fetchall():
            universities.append({
                'university_id': row.university_id,
                'name_ar': row.name_ar
            })

        conn.close()
        return jsonify(universities)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/colleges/list', methods=['GET'])
def get_colleges_list():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        university_id = request.args.get('university_id')
        if university_id:
            cursor.execute("""
                SELECT college_id, name_ar
                FROM colleges
                WHERE university_id = ?
                ORDER BY name_ar
            """, (university_id,))
        else:
            cursor.execute("SELECT college_id, name_ar FROM colleges ORDER BY name_ar")

        colleges = []
        for row in cursor.fetchall():
            colleges.append({
                'college_id': row.college_id,
                'name_ar': row.name_ar
            })

        conn.close()
        return jsonify(colleges)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/specializations/list', methods=['GET'])
def get_specializations_list():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT specialization_id, name FROM specializations ORDER BY name")

        specializations = []
        for row in cursor.fetchall():
            specializations.append({
                'specialization_id': row.specialization_id,
                'name': row.name
            })

        conn.close()
        return jsonify(specializations)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

# API للأقسام
@app.route('/api/departments', methods=['GET'])
def get_departments():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT d.department_id, d.college_id, d.name, d.head_name,
                   d.secretary_name, d.description, d.created_date, c.name_ar as college_name
            FROM departments d
            LEFT JOIN colleges c ON d.college_id = c.college_id
            ORDER BY d.created_date DESC
        """)

        departments = []
        for row in cursor.fetchall():
            departments.append({
                'department_id': row.department_id,
                'college_id': row.college_id,
                'name': row.name,
                'head_name': row.head_name,
                'secretary_name': row.secretary_name,
                'description': row.description,
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None,
                'college_name': row.college_name
            })

        conn.close()
        return jsonify(departments)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/departments', methods=['POST'])
def add_department():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم القسم مطلوب'}), 400

        if not data.get('college_id'):
            return jsonify({'error': 'الكلية مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO departments (college_id, name, head_name, secretary_name, description, created_date)
            VALUES (?, ?, ?, ?, ?, GETDATE())
        """, (data.get('college_id'), data.get('name'), data.get('head_name'),
              data.get('secretary_name'), data.get('description')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة قسم', 'INSERT', f"قسم: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة القسم بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة القسم: {str(e)}'}), 500

@app.route('/api/departments/<int:department_id>', methods=['PUT'])
def update_department(department_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم القسم مطلوب'}), 400

        if not data.get('college_id'):
            return jsonify({'error': 'الكلية مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE departments
            SET college_id = ?, name = ?, head_name = ?, secretary_name = ?, description = ?
            WHERE department_id = ?
        """, (data.get('college_id'), data.get('name'), data.get('head_name'),
              data.get('secretary_name'), data.get('description'), department_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'القسم غير موجود'}), 404

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تعديل قسم', 'UPDATE', f"قسم: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تعديل القسم بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تعديل القسم: {str(e)}'}), 500

@app.route('/api/departments/<int:department_id>', methods=['DELETE'])
def delete_department(department_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود طلبة مرتبطين بالقسم
        cursor.execute("SELECT COUNT(*) FROM students WHERE department_id = ?", (department_id,))
        student_count = cursor.fetchone()[0]

        if student_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف القسم لوجود {student_count} طالب مرتبط به'}), 400

        # الحصول على اسم القسم قبل الحذف
        cursor.execute("SELECT name FROM departments WHERE department_id = ?", (department_id,))
        department = cursor.fetchone()

        if not department:
            conn.close()
            return jsonify({'error': 'القسم غير موجود'}), 404

        department_name = department.name

        cursor.execute("DELETE FROM departments WHERE department_id = ?", (department_id,))

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف قسم', 'DELETE', f"قسم: {department_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف القسم بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف القسم: {str(e)}'}), 500

@app.route('/departments')
def departments():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('departments.html')

# API للتخصصات
@app.route('/api/specializations', methods=['GET'])
def get_specializations():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT specialization_id, name, created_date, updated_date
            FROM specializations
            ORDER BY created_date DESC
        """)

        specializations = []
        for row in cursor.fetchall():
            specializations.append({
                'specialization_id': row.specialization_id,
                'name': row.name,
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None,
                'updated_date': row.updated_date.strftime('%Y-%m-%d') if row.updated_date else None
            })

        conn.close()
        return jsonify(specializations)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/specializations', methods=['POST'])
def add_specialization():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم التخصص مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO specializations (name, created_date, updated_date)
            VALUES (?, GETDATE(), GETDATE())
        """, (data.get('name'),))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة تخصص', 'INSERT', f"تخصص: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة التخصص بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة التخصص: {str(e)}'}), 500

@app.route('/api/specializations/<int:specialization_id>', methods=['PUT'])
def update_specialization(specialization_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم التخصص مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE specializations
            SET name = ?, updated_date = GETDATE()
            WHERE specialization_id = ?
        """, (data.get('name'), specialization_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'التخصص غير موجود'}), 404

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تعديل تخصص', 'UPDATE', f"تخصص: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تعديل التخصص بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في تعديل التخصص: {str(e)}'}), 500

@app.route('/api/specializations/<int:specialization_id>', methods=['DELETE'])
def delete_specialization(specialization_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود طلبة مرتبطين بالتخصص
        cursor.execute("SELECT COUNT(*) FROM students WHERE specialization_id = ?", (specialization_id,))
        student_count = cursor.fetchone()[0]

        if student_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف التخصص لوجود {student_count} طالب مرتبط به'}), 400

        # الحصول على اسم التخصص قبل الحذف
        cursor.execute("SELECT name FROM specializations WHERE specialization_id = ?", (specialization_id,))
        specialization = cursor.fetchone()

        if not specialization:
            conn.close()
            return jsonify({'error': 'التخصص غير موجود'}), 404

        specialization_name = specialization.name

        cursor.execute("DELETE FROM specializations WHERE specialization_id = ?", (specialization_id,))

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف تخصص', 'DELETE', f"تخصص: {specialization_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف التخصص بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف التخصص: {str(e)}'}), 500

@app.route('/api/departments/list', methods=['GET'])
def get_departments_list():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT department_id, name FROM departments ORDER BY name")

        departments = []
        for row in cursor.fetchall():
            departments.append({
                'department_id': row.department_id,
                'name': row.name
            })

        conn.close()
        return jsonify(departments)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/specializations')
def specializations():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('specializations.html')

@app.route('/academic_programs')
def academic_programs():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('academic_programs.html')

@app.route('/academic_years')
def academic_years():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('academic_years.html')

@app.route('/semesters')
def semesters():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('semesters.html')

@app.route('/admission_channels')
def admission_channels():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('admission_channels.html')

@app.route('/subjects')
def subjects():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('subjects.html')

# مسارات الصفحات الإضافية محذوفة لتجنب التكرار

# API للأعوام الدراسية
@app.route('/api/academic_years', methods=['GET'])
def get_academic_years():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT year_id, name, created_date, updated_date
            FROM academic_years
            ORDER BY name DESC
        """)

        academic_years = []
        for row in cursor.fetchall():
            academic_years.append({
                'year_id': row.year_id,
                'name': row.name,
                'created_date': row.created_date.isoformat() if row.created_date else None,
                'updated_date': row.updated_date.isoformat() if row.updated_date else None
            })

        conn.close()
        return jsonify(academic_years)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/academic_years', methods=['POST'])
def add_academic_year():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم العام الدراسي مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO academic_years (name)
            VALUES (?)
        """, (data.get('name'),))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة عام دراسي', 'INSERT', f"عام دراسي: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة العام الدراسي بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة العام الدراسي: {str(e)}'}), 500

@app.route('/api/academic_years/<int:year_id>', methods=['PUT'])
def update_academic_year(year_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم العام الدراسي مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE academic_years
            SET name = ?, updated_date = GETDATE()
            WHERE year_id = ?
        """, (data.get('name'), year_id))

        if cursor.rowcount == 0:
            return jsonify({'error': 'العام الدراسي غير موجود'}), 404

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث عام دراسي', 'UPDATE', f"عام دراسي: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث العام الدراسي بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث العام الدراسي: {str(e)}'}), 500

@app.route('/api/academic_years/<int:year_id>', methods=['DELETE'])
def delete_academic_year(year_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود العام الدراسي
        cursor.execute("SELECT name FROM academic_years WHERE year_id = ?", (year_id,))
        year = cursor.fetchone()

        if not year:
            return jsonify({'error': 'العام الدراسي غير موجود'}), 404

        # التحقق من وجود فصول دراسية مرتبطة
        cursor.execute("SELECT COUNT(*) FROM semesters WHERE year_id = ?", (year_id,))
        semester_count = cursor.fetchone()[0]

        if semester_count > 0:
            return jsonify({'error': 'لا يمكن حذف العام الدراسي لوجود فصول دراسية مرتبطة به'}), 400

        # التحقق من وجود درجات مرتبطة
        cursor.execute("SELECT COUNT(*) FROM grades WHERE academic_year_id = ?", (year_id,))
        grade_count = cursor.fetchone()[0]

        if grade_count > 0:
            return jsonify({'error': 'لا يمكن حذف العام الدراسي لوجود درجات مرتبطة به'}), 400

        # حذف العام الدراسي
        cursor.execute("DELETE FROM academic_years WHERE year_id = ?", (year_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف عام دراسي', 'DELETE', f"عام دراسي: {year.name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف العام الدراسي بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف العام الدراسي: {str(e)}'}), 500

# API للفصول الدراسية
@app.route('/api/semesters', methods=['GET'])
def get_semesters():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.semester_id, s.year_id, s.name, s.created_date, s.updated_date, ay.name as year_name
            FROM semesters s
            LEFT JOIN academic_years ay ON s.year_id = ay.year_id
            ORDER BY s.name
        """)

        semesters = []
        for row in cursor.fetchall():
            semesters.append({
                'semester_id': row.semester_id,
                'year_id': row.year_id,
                'name': row.name,
                'year_name': row.year_name,
                'created_date': row.created_date.isoformat() if row.created_date else None,
                'updated_date': row.updated_date.isoformat() if row.updated_date else None
            })

        conn.close()
        return jsonify(semesters)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/semesters', methods=['POST'])
def add_semester():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم الفصل الدراسي مطلوب'}), 400

        if not data.get('year_id'):
            return jsonify({'error': 'العام الدراسي مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار اسم الفصل الدراسي في نفس العام الدراسي
        cursor.execute("SELECT COUNT(*) FROM semesters WHERE name = ? AND year_id = ?",
                      (data.get('name'), data.get('year_id')))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': 'اسم الفصل الدراسي موجود مسبقاً في نفس العام الدراسي'}), 400

        cursor.execute("""
            INSERT INTO semesters (name, year_id)
            VALUES (?, ?)
        """, (data.get('name'), data.get('year_id')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة فصل دراسي', 'INSERT', f"فصل دراسي: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة الفصل الدراسي بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة الفصل الدراسي: {str(e)}'}), 500

@app.route('/api/semesters/<int:semester_id>', methods=['PUT'])
def update_semester(semester_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم الفصل الدراسي مطلوب'}), 400

        if not data.get('year_id'):
            return jsonify({'error': 'العام الدراسي مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار اسم الفصل الدراسي في نفس العام الدراسي (باستثناء الفصل الحالي)
        cursor.execute("SELECT COUNT(*) FROM semesters WHERE name = ? AND year_id = ? AND semester_id != ?",
                      (data.get('name'), data.get('year_id'), semester_id))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': 'اسم الفصل الدراسي موجود مسبقاً في نفس العام الدراسي'}), 400

        cursor.execute("""
            UPDATE semesters
            SET name = ?, year_id = ?, updated_date = GETDATE()
            WHERE semester_id = ?
        """, (data.get('name'), data.get('year_id'), semester_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'الفصل الدراسي غير موجود'}), 404

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تعديل فصل دراسي', 'UPDATE', f"فصل دراسي: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تعديل الفصل الدراسي بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تعديل الفصل الدراسي: {str(e)}'}), 500

@app.route('/api/semesters/<int:semester_id>', methods=['DELETE'])
def delete_semester(semester_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الفصل الدراسي
        cursor.execute("SELECT name FROM semesters WHERE semester_id = ?", (semester_id,))
        semester = cursor.fetchone()

        if not semester:
            conn.close()
            return jsonify({'error': 'الفصل الدراسي غير موجود'}), 404

        semester_name = semester.name

        # التحقق من وجود درجات مرتبطة
        cursor.execute("SELECT COUNT(*) FROM grades WHERE semester_id = ?", (semester_id,))
        grade_count = cursor.fetchone()[0]

        if grade_count > 0:
            conn.close()
            return jsonify({'error': 'لا يمكن حذف الفصل الدراسي لوجود درجات مرتبطة به'}), 400

        # حذف الفصل الدراسي
        cursor.execute("DELETE FROM semesters WHERE semester_id = ?", (semester_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف فصل دراسي', 'DELETE', f"فصل دراسي: {semester_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف الفصل الدراسي بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف الفصل الدراسي: {str(e)}'}), 500

# API للمواد الدراسية
@app.route('/api/subjects', methods=['GET'])
def get_subjects():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.subject_id, s.program_id, s.name, s.units, s.hours,
                   s.subject_type, s.academic_year, s.created_date,
                   ap.name as program_name
            FROM subjects s
            LEFT JOIN academic_programs ap ON s.program_id = ap.program_id
            ORDER BY s.created_date DESC
        """)

        subjects = []
        for row in cursor.fetchall():
            subjects.append({
                'subject_id': row.subject_id,
                'program_id': row.program_id,
                'name': row.name,
                'units': row.units,
                'hours': row.hours,
                'subject_type': row.subject_type,
                'academic_year': row.academic_year,
                'program_name': row.program_name,
                'created_date': row.created_date.isoformat() if row.created_date else None
            })

        conn.close()
        return jsonify(subjects)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/subjects', methods=['POST'])
def add_subject():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        required_fields = ['name', 'units', 'hours', 'subject_type', 'academic_year', 'program_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'حقل {field} مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO subjects (program_id, name, units, hours, subject_type, academic_year)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (data.get('program_id'), data.get('name'), data.get('units'),
              data.get('hours'), data.get('subject_type'), data.get('academic_year')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة مادة دراسية', 'INSERT', f"مادة: {data.get('subject_name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة المادة الدراسية بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة المادة الدراسية: {str(e)}'}), 500

@app.route('/api/subjects/<int:subject_id>', methods=['PUT'])
def update_subject(subject_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        required_fields = ['name', 'units', 'hours', 'subject_type', 'academic_year', 'program_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'حقل {field} مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE subjects
            SET program_id = ?, name = ?, units = ?, hours = ?, subject_type = ?,
                academic_year = ?, updated_date = GETDATE()
            WHERE subject_id = ?
        """, (data.get('program_id'), data.get('name'), data.get('units'),
              data.get('hours'), data.get('subject_type'), data.get('academic_year'), subject_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'المادة الدراسية غير موجودة'}), 404

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث مادة دراسية', 'UPDATE', f"مادة: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث المادة الدراسية بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث المادة الدراسية: {str(e)}'}), 500

@app.route('/api/subjects/<int:subject_id>', methods=['DELETE'])
def delete_subject(subject_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود المادة الدراسية
        cursor.execute("SELECT name FROM subjects WHERE subject_id = ?", (subject_id,))
        subject = cursor.fetchone()

        if not subject:
            conn.close()
            return jsonify({'error': 'المادة الدراسية غير موجودة'}), 404

        subject_name = subject.name

        # التحقق من وجود درجات مرتبطة
        cursor.execute("SELECT COUNT(*) FROM grades WHERE subject_id = ?", (subject_id,))
        grade_count = cursor.fetchone()[0]

        if grade_count > 0:
            return jsonify({'error': 'لا يمكن حذف المادة الدراسية لوجود درجات مرتبطة بها'}), 400

        # حذف المادة الدراسية
        cursor.execute("DELETE FROM subjects WHERE subject_id = ?", (subject_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف مادة دراسية', 'DELETE', f"مادة: {subject_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف المادة الدراسية بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف المادة الدراسية: {str(e)}'}), 500

# API للبرامج الأكاديمية
@app.route('/api/academic_programs', methods=['GET'])
def get_academic_programs():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT ap.program_id, ap.department_id, ap.name, ap.program_type, ap.duration,
                   ap.created_date, d.name as department_name
            FROM academic_programs ap
            LEFT JOIN departments d ON ap.department_id = d.department_id
            ORDER BY ap.name
        """)

        programs = []
        for row in cursor.fetchall():
            programs.append({
                'program_id': row.program_id,
                'department_id': row.department_id,
                'name': row.name,
                'program_type': row.program_type,
                'duration': row.duration,
                'department_name': row.department_name,
                'created_date': row.created_date.isoformat() if row.created_date else None
            })

        conn.close()
        return jsonify(programs)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البرامج الأكاديمية: {str(e)}'}), 500

@app.route('/api/academic_programs', methods=['POST'])
def add_academic_program():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        required_fields = ['name', 'program_type', 'duration', 'department_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'حقل {field} مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار اسم البرنامج
        cursor.execute("SELECT COUNT(*) FROM academic_programs WHERE name = ?", (data.get('name'),))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': 'اسم البرنامج الأكاديمي موجود مسبقاً'}), 400

        cursor.execute("""
            INSERT INTO academic_programs (department_id, name, program_type, duration)
            VALUES (?, ?, ?, ?)
        """, (data.get('department_id'), data.get('name'), data.get('program_type'), data.get('duration')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة برنامج أكاديمي', 'INSERT', f"برنامج: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة البرنامج الأكاديمي بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة البرنامج الأكاديمي: {str(e)}'}), 500

@app.route('/api/academic_programs/<int:program_id>', methods=['PUT'])
def update_academic_program(program_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        required_fields = ['name', 'program_type', 'duration', 'department_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'حقل {field} مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار اسم البرنامج (باستثناء البرنامج الحالي)
        cursor.execute("SELECT COUNT(*) FROM academic_programs WHERE name = ? AND program_id != ?",
                      (data.get('name'), program_id))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': 'اسم البرنامج الأكاديمي موجود مسبقاً'}), 400

        cursor.execute("""
            UPDATE academic_programs
            SET department_id = ?, name = ?, program_type = ?, duration = ?, updated_date = GETDATE()
            WHERE program_id = ?
        """, (data.get('department_id'), data.get('name'), data.get('program_type'),
              data.get('duration'), program_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'البرنامج الأكاديمي غير موجود'}), 404

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث برنامج أكاديمي', 'UPDATE', f"برنامج: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث البرنامج الأكاديمي بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث البرنامج الأكاديمي: {str(e)}'}), 500

@app.route('/api/academic_programs/<int:program_id>', methods=['DELETE'])
def delete_academic_program(program_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود البرنامج الأكاديمي
        cursor.execute("SELECT name FROM academic_programs WHERE program_id = ?", (program_id,))
        program = cursor.fetchone()

        if not program:
            conn.close()
            return jsonify({'error': 'البرنامج الأكاديمي غير موجود'}), 404

        program_name = program.name

        # التحقق من وجود مواد مرتبطة
        cursor.execute("SELECT COUNT(*) FROM subjects WHERE program_id = ?", (program_id,))
        subject_count = cursor.fetchone()[0]

        if subject_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف البرنامج الأكاديمي لوجود {subject_count} مادة مرتبطة به'}), 400

        # التحقق من وجود طلبة مرتبطين
        cursor.execute("SELECT COUNT(*) FROM students WHERE program_id = ?", (program_id,))
        student_count = cursor.fetchone()[0]

        if student_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف البرنامج الأكاديمي لوجود {student_count} طالب مرتبط به'}), 400

        # حذف البرنامج الأكاديمي
        cursor.execute("DELETE FROM academic_programs WHERE program_id = ?", (program_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف برنامج أكاديمي', 'DELETE', f"برنامج: {program_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف البرنامج الأكاديمي بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف البرنامج الأكاديمي: {str(e)}'}), 500

# API لقنوات القبول
@app.route('/api/admission_channels', methods=['GET'])
def get_admission_channels():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT channel_id, name, created_date, updated_date
            FROM admission_channels
            ORDER BY name
        """)

        channels = []
        for row in cursor.fetchall():
            channels.append({
                'channel_id': row.channel_id,
                'name': row.name,
                'created_date': row.created_date.isoformat() if row.created_date else None,
                'updated_date': row.updated_date.isoformat() if row.updated_date else None
            })

        conn.close()
        return jsonify(channels)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/admission_channels', methods=['POST'])
def add_admission_channel():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم قناة القبول مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار اسم قناة القبول
        cursor.execute("SELECT COUNT(*) FROM admission_channels WHERE name = ?", (data.get('name'),))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': 'اسم قناة القبول موجود مسبقاً'}), 400

        cursor.execute("""
            INSERT INTO admission_channels (name)
            VALUES (?)
        """, (data.get('name'),))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة قناة قبول', 'INSERT', f"قناة قبول: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة قناة القبول بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة قناة القبول: {str(e)}'}), 500

@app.route('/api/admission_channels/<int:channel_id>', methods=['PUT'])
def update_admission_channel(channel_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        if not data.get('name'):
            return jsonify({'error': 'اسم قناة القبول مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم تكرار اسم قناة القبول (باستثناء القناة الحالية)
        cursor.execute("SELECT COUNT(*) FROM admission_channels WHERE name = ? AND channel_id != ?",
                      (data.get('name'), channel_id))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({'error': 'اسم قناة القبول موجود مسبقاً'}), 400

        cursor.execute("""
            UPDATE admission_channels
            SET name = ?, updated_date = GETDATE()
            WHERE channel_id = ?
        """, (data.get('name'), channel_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({'error': 'قناة القبول غير موجودة'}), 404

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث قناة قبول', 'UPDATE', f"قناة قبول: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث قناة القبول بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث قناة القبول: {str(e)}'}), 500

@app.route('/api/admission_channels/<int:channel_id>', methods=['DELETE'])
def delete_admission_channel(channel_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود قناة القبول
        cursor.execute("SELECT name FROM admission_channels WHERE channel_id = ?", (channel_id,))
        channel = cursor.fetchone()

        if not channel:
            conn.close()
            return jsonify({'error': 'قناة القبول غير موجودة'}), 404

        channel_name = channel.name

        # التحقق من وجود طلبة مرتبطين بقناة القبول
        cursor.execute("SELECT COUNT(*) FROM students WHERE channel_id = ?", (channel_id,))
        student_count = cursor.fetchone()[0]

        if student_count > 0:
            conn.close()
            return jsonify({'error': f'لا يمكن حذف قناة القبول لوجود {student_count} طالب مرتبط بها'}), 400

        # حذف قناة القبول
        cursor.execute("DELETE FROM admission_channels WHERE channel_id = ?", (channel_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف قناة قبول', 'DELETE', f"قناة قبول: {channel_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف قناة القبول بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف قناة القبول: {str(e)}'}), 500

# Faculty Management Routes
# API لأعضاء هيئة التدريس
@app.route('/api/faculty', methods=['GET'])
def get_faculty():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT f.faculty_id, f.name, f.degree, f.academic_title, f.title_date,
                   f.general_specialization, f.specific_specialization, f.department_id,
                   f.mobile_phone, f.email, f.notes, f.created_date, f.updated_date,
                   d.name as department_name
            FROM faculty f
            LEFT JOIN departments d ON f.department_id = d.department_id
            ORDER BY f.created_date DESC
        """)

        faculty_members = []
        for row in cursor.fetchall():
            faculty_members.append({
                'faculty_id': row.faculty_id,
                'name': row.name,
                'degree': row.degree,
                'academic_title': row.academic_title,
                'title_date': row.title_date.strftime('%Y-%m-%d') if row.title_date else None,
                'general_specialization': row.general_specialization,
                'specific_specialization': row.specific_specialization,
                'department_id': row.department_id,
                'department_name': row.department_name,
                'mobile_phone': row.mobile_phone,
                'email': row.email,
                'notes': row.notes,
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None,
                'updated_date': row.updated_date.strftime('%Y-%m-%d') if row.updated_date else None
            })

        conn.close()
        return jsonify(faculty_members)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/faculty', methods=['POST'])
def add_faculty():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'الاسم مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO faculty (name, degree, academic_title, title_date,
                               general_specialization, specific_specialization, department_id,
                               mobile_phone, email, notes, created_date, updated_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), GETDATE())
        """, (data.get('name'), data.get('degree'), data.get('academic_title'),
              data.get('title_date'), data.get('general_specialization'),
              data.get('specific_specialization'), data.get('department_id'),
              data.get('mobile_phone'), data.get('email'), data.get('notes')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة عضو هيئة تدريس', 'INSERT', f"عضو هيئة تدريس: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة عضو هيئة التدريس بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة عضو هيئة التدريس: {str(e)}'}), 500

@app.route('/api/faculty/<int:faculty_id>', methods=['PUT'])
def update_faculty(faculty_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        if not data.get('name') or not data.get('department_id'):
            return jsonify({'error': 'الاسم والقسم مطلوبان'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE faculty
            SET name = ?, degree = ?, academic_title = ?, title_date = ?,
                general_specialization = ?, specific_specialization = ?, department_id = ?,
                mobile_phone = ?, email = ?, notes = ?, updated_date = GETDATE()
            WHERE faculty_id = ?
        """, (data.get('name'), data.get('degree'), data.get('academic_title'),
              data.get('title_date'), data.get('general_specialization'),
              data.get('specific_specialization'), data.get('department_id'),
              data.get('mobile_phone'), data.get('email'), data.get('notes'), faculty_id))

        if cursor.rowcount == 0:
            return jsonify({'error': 'عضو هيئة التدريس غير موجود'}), 404

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث عضو هيئة تدريس', 'UPDATE', f"عضو هيئة تدريس: {data.get('full_name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث بيانات عضو هيئة التدريس بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث عضو هيئة التدريس: {str(e)}'}), 500

@app.route('/api/faculty/<int:faculty_id>', methods=['DELETE'])
def delete_faculty(faculty_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود عضو هيئة التدريس
        cursor.execute("SELECT name FROM faculty WHERE faculty_id = ?", (faculty_id,))
        faculty_member = cursor.fetchone()

        if not faculty_member:
            return jsonify({'error': 'عضو هيئة التدريس غير موجود'}), 404

        # التحقق من وجود درجات مرتبطة (إذا كان جدول grades موجود)
        try:
            cursor.execute("SELECT COUNT(*) FROM grades WHERE instructor_id = ?", (faculty_id,))
            grade_count = cursor.fetchone()[0]
            if grade_count > 0:
                return jsonify({'error': 'لا يمكن حذف عضو هيئة التدريس لوجود درجات مرتبطة به'}), 400
        except:
            # إذا لم يكن جدول grades موجود أو لا يحتوي على instructor_id، نتجاهل هذا التحقق
            pass

        # حذف عضو هيئة التدريس
        cursor.execute("DELETE FROM faculty WHERE faculty_id = ?", (faculty_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف عضو هيئة تدريس', 'DELETE', f"عضو هيئة تدريس: {faculty_member.name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف عضو هيئة التدريس بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف عضو هيئة التدريس: {str(e)}'}), 500

@app.route('/faculty')
def faculty():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('faculty.html')

# Student Management Routes


@app.route('/api/students', methods=['POST'])
def add_student():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        # التحقق من الحقول المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'اسم الطالب مطلوب'}), 400

        if not data.get('gender'):
            return jsonify({'error': 'الجنس مطلوب'}), 400

        if not data.get('program_id'):
            return jsonify({'error': 'البرنامج الأكاديمي مطلوب'}), 400

        if not data.get('channel_id'):
            return jsonify({'error': 'قناة القبول مطلوبة'}), 400

        if not data.get('year_id'):
            return jsonify({'error': 'السنة الأكاديمية مطلوبة'}), 400

        if not data.get('status'):
            return jsonify({'error': 'الحالة مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO students (name, gender, program_id, channel_id, specialization_id,
                                year_id, status, created_date, updated_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, GETDATE(), GETDATE())
        """, (data.get('name'), data.get('gender'), data.get('program_id'),
              data.get('channel_id'), data.get('specialization_id'),
              data.get('year_id'), data.get('status')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة طالب', 'INSERT', f"طالب: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة الطالب بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة الطالب: {str(e)}'}), 500

@app.route('/api/students/<int:student_id>', methods=['PUT'])
def update_student(student_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        # التحقق من الحقول المطلوبة
        if not data.get('name'):
            return jsonify({'error': 'اسم الطالب مطلوب'}), 400

        if not data.get('gender'):
            return jsonify({'error': 'الجنس مطلوب'}), 400

        if not data.get('program_id'):
            return jsonify({'error': 'البرنامج الأكاديمي مطلوب'}), 400

        if not data.get('channel_id'):
            return jsonify({'error': 'قناة القبول مطلوبة'}), 400

        if not data.get('year_id'):
            return jsonify({'error': 'السنة الأكاديمية مطلوبة'}), 400

        if not data.get('status'):
            return jsonify({'error': 'الحالة مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الطالب
        cursor.execute("SELECT student_id FROM students WHERE student_id = ?", (student_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({'error': 'الطالب غير موجود'}), 404

        cursor.execute("""
            UPDATE students
            SET name = ?, gender = ?, program_id = ?, channel_id = ?,
                specialization_id = ?, year_id = ?, status = ?, updated_date = GETDATE()
            WHERE student_id = ?
        """, (data.get('name'), data.get('gender'), data.get('program_id'),
              data.get('channel_id'), data.get('specialization_id'),
              data.get('year_id'), data.get('status'), student_id))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث طالب', 'UPDATE', f"طالب: {data.get('name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث بيانات الطالب بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث بيانات الطالب: {str(e)}'}), 500

@app.route('/api/students/<int:student_id>', methods=['DELETE'])
def delete_student(student_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الطالب والحصول على اسمه
        cursor.execute("SELECT name FROM students WHERE student_id = ?", (student_id,))
        student = cursor.fetchone()

        if not student:
            conn.close()
            return jsonify({'error': 'الطالب غير موجود'}), 404

        student_name = student.name

        # حذف الطالب
        cursor.execute("DELETE FROM students WHERE student_id = ?", (student_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف طالب', 'DELETE', f"طالب: {student_name}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف الطالب بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف الطالب: {str(e)}'}), 500

@app.route('/api/students', methods=['GET'])
def get_students_by_program():
    """جلب الطلبة حسب البرنامج الأكاديمي والعام الدراسي"""
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        program_id = request.args.get('program_id')
        year_id = request.args.get('year_id')

        conn = get_db_connection()
        cursor = conn.cursor()

        if program_id and year_id:
            cursor.execute("""
                SELECT student_id, name FROM students
                WHERE program_id = ? AND year_id = ?
                ORDER BY name
            """, (program_id, year_id))
        elif program_id:
            cursor.execute("""
                SELECT student_id, name FROM students
                WHERE program_id = ?
                ORDER BY name
            """, (program_id,))
        else:
            cursor.execute("""
                SELECT student_id, name FROM students
                ORDER BY name
            """)

        students = []
        for row in cursor.fetchall():
            students.append({
                'student_id': row.student_id,
                'name': row.name
            })

        conn.close()
        return jsonify(students)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل الطلبة: {str(e)}'}), 500

@app.route('/api/students/full', methods=['GET'])
def get_students_full():
    """جلب جميع الطلبة مع معلومات كاملة لصفحة الطلبة"""
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.student_id, s.name, s.gender, s.program_id, s.channel_id,
                   s.specialization_id, s.year_id, s.status, s.created_date,
                   sp.name as specialization_name, ap.name as program_name,
                   ac.name as channel_name, ay.name as year_name
            FROM students s
            LEFT JOIN specializations sp ON s.specialization_id = sp.specialization_id
            LEFT JOIN academic_programs ap ON s.program_id = ap.program_id
            LEFT JOIN admission_channels ac ON s.channel_id = ac.channel_id
            LEFT JOIN academic_years ay ON s.year_id = ay.year_id
            ORDER BY s.created_date DESC
        """)

        students = []
        for row in cursor.fetchall():
            students.append({
                'student_id': row.student_id,
                'name': row.name,
                'gender': row.gender,
                'program_id': row.program_id,
                'channel_id': row.channel_id,
                'specialization_id': row.specialization_id,
                'year_id': row.year_id,
                'status': row.status,
                'specialization_name': row.specialization_name,
                'program_name': row.program_name,
                'channel_name': row.channel_name,
                'year_name': row.year_name,
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None
            })

        conn.close()
        return jsonify(students)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/students')
def students():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('students.html')



@app.route('/test_apis')
def test_apis():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('test_apis.html')

@app.route('/test_add_faculty')
def test_add_faculty():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('test_add_faculty.html')

@app.route('/test_login')
def test_login():
    return render_template('test_login_redirect.html')

@app.route('/test_users_debug')
def test_users_debug():
    return render_template('test_users_debug.html')

@app.route('/users_simple')
def users_simple():
    return render_template('users_simple.html')

@app.route('/users_minimal')
def users_minimal():
    return render_template('users_minimal.html')

@app.route('/users_fixed')
def users_fixed():
    return render_template('users_fixed.html')

@app.route('/test_api')
def test_api():
    return render_template('test_api.html')

@app.route('/debug_users')
def debug_users():
    return render_template('debug_users.html')

# Users API Routes
@app.route('/api/users', methods=['GET'])
def get_users():
    # تعطيل الأمان مؤقتاً للاختبار
    # if 'user_id' not in session:
    #     return jsonify({"error": "غير مصرح"}), 401

    # if session.get('user_type') != 'مدير النظام':
    #     return jsonify({"error": "ليس لديك صلاحية للوصول إلى هذه البيانات"}), 403

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT user_id, username, full_name, user_type, phone, email, status,
                   created_date, updated_date
            FROM users
            ORDER BY created_date DESC
        """)

        users = []
        for row in cursor.fetchall():
            user = {
                'user_id': row.user_id,
                'username': row.username,
                'full_name': row.full_name,
                'user_type': row.user_type,
                'phone': row.phone,
                'email': row.email,
                'status': row.status,
                'last_login': None,  # لا يوجد حقل last_login في الجدول
                'created_date': row.created_date.isoformat() if row.created_date else None,
                'updated_date': row.updated_date.isoformat() if row.updated_date else None,
                'notes': None  # لا يوجد حقل notes في الجدول
            }
            users.append(user)

        conn.close()
        return jsonify(users)

    except Exception as e:
        print(f"خطأ في جلب المستخدمين: {str(e)}")
        return jsonify({"error": f"خطأ في جلب البيانات: {str(e)}"}), 500

@app.route('/api/users', methods=['POST'])
def add_user():
    # تعطيل الأمان مؤقتاً للاختبار
    # if 'user_id' not in session:
    #     return jsonify({"error": "غير مصرح"}), 401

    # if session.get('user_type') != 'مدير النظام':
    #     return jsonify({"error": "ليس لديك صلاحية لإضافة مستخدمين"}), 403

    try:
        data = request.get_json()

        # التحقق من البيانات المطلوبة
        required_fields = ['username', 'password', 'full_name', 'user_type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"الحقل {field} مطلوب"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود اسم المستخدم مسبقاً
        cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (data['username'],))
        if cursor.fetchone()[0] > 0:
            conn.close()
            return jsonify({"error": "اسم المستخدم موجود مسبقاً"}), 400

        # تشفير كلمة المرور
        password_hash = generate_password_hash(data['password'])

        # إدراج المستخدم الجديد
        cursor.execute("""
            INSERT INTO users (username, password_hash, full_name, user_type, phone, email, status)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            data['username'],
            password_hash,
            data['full_name'],
            data['user_type'],
            data.get('phone'),
            data.get('email'),
            data.get('status', 'نشط')
        ))

        conn.commit()

        # الحصول على ID المستخدم الجديد
        cursor.execute("SELECT @@IDENTITY")
        user_id = cursor.fetchone()[0]

        conn.close()

        return jsonify({"message": "تم إضافة المستخدم بنجاح", "user_id": int(user_id)}), 201

    except Exception as e:
        print(f"خطأ في إضافة المستخدم: {str(e)}")
        return jsonify({"error": f"خطأ في إضافة المستخدم: {str(e)}"}), 500

@app.route('/api/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    # تعطيل الأمان مؤقتاً للاختبار
    # if 'user_id' not in session:
    #     return jsonify({"error": "غير مصرح"}), 401

    # if session.get('user_type') != 'مدير النظام':
    #     return jsonify({"error": "ليس لديك صلاحية لتعديل المستخدمين"}), 403

    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود المستخدم
        cursor.execute("SELECT COUNT(*) FROM users WHERE user_id = ?", (user_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({"error": "المستخدم غير موجود"}), 404

        # التحقق من عدم تكرار اسم المستخدم (إذا تم تغييره)
        if 'username' in data:
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = ? AND user_id != ?",
                         (data['username'], user_id))
            if cursor.fetchone()[0] > 0:
                conn.close()
                return jsonify({"error": "اسم المستخدم موجود مسبقاً"}), 400

        # بناء استعلام التحديث
        update_fields = []
        update_values = []

        allowed_fields = ['username', 'full_name', 'user_type', 'phone', 'email', 'status']
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = ?")
                update_values.append(data[field])

        if update_fields:
            update_fields.append("updated_date = GETDATE()")
            update_values.append(user_id)

            update_query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ?"
            cursor.execute(update_query, update_values)
            conn.commit()

        conn.close()
        return jsonify({"message": "تم تحديث بيانات المستخدم بنجاح"}), 200

    except Exception as e:
        print(f"خطأ في تحديث المستخدم: {str(e)}")
        return jsonify({"error": f"خطأ في تحديث المستخدم: {str(e)}"}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
def delete_user(user_id):
    # تعطيل الأمان مؤقتاً للاختبار
    # if 'user_id' not in session:
    #     return jsonify({"error": "غير مصرح"}), 401

    # if session.get('user_type') != 'مدير النظام':
    #     return jsonify({"error": "ليس لديك صلاحية لحذف المستخدمين"}), 403

    # منع حذف المستخدم الحالي
    if session.get('user_id') == user_id:
        return jsonify({"error": "لا يمكنك حذف حسابك الخاص"}), 400

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود المستخدم
        cursor.execute("SELECT COUNT(*) FROM users WHERE user_id = ?", (user_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({"error": "المستخدم غير موجود"}), 404

        # حذف المستخدم
        cursor.execute("DELETE FROM users WHERE user_id = ?", (user_id,))
        conn.commit()
        conn.close()

        return jsonify({"message": "تم حذف المستخدم بنجاح"}), 200

    except Exception as e:
        print(f"خطأ في حذف المستخدم: {str(e)}")
        return jsonify({"error": f"خطأ في حذف المستخدم: {str(e)}"}), 500

@app.route('/api/users/<int:user_id>/reset-password', methods=['POST'])
def reset_user_password(user_id):
    # تعطيل الأمان مؤقتاً للاختبار
    # if 'user_id' not in session:
    #     return jsonify({"error": "غير مصرح"}), 401

    # if session.get('user_type') != 'مدير النظام':
    #     return jsonify({"error": "ليس لديك صلاحية لإعادة تعيين كلمات المرور"}), 403

    try:
        data = request.get_json()
        new_password = data.get('new_password')

        if not new_password:
            return jsonify({"error": "كلمة المرور الجديدة مطلوبة"}), 400

        if len(new_password) < 6:
            return jsonify({"error": "كلمة المرور يجب أن تكون 6 أحرف على الأقل"}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود المستخدم
        cursor.execute("SELECT COUNT(*) FROM users WHERE user_id = ?", (user_id,))
        if cursor.fetchone()[0] == 0:
            conn.close()
            return jsonify({"error": "المستخدم غير موجود"}), 404

        # تشفير كلمة المرور الجديدة
        password_hash = generate_password_hash(new_password)

        # تحديث كلمة المرور
        cursor.execute("""
            UPDATE users
            SET password_hash = ?, updated_date = GETDATE()
            WHERE user_id = ?
        """, (password_hash, user_id))

        conn.commit()
        conn.close()

        return jsonify({"message": "تم إعادة تعيين كلمة المرور بنجاح"}), 200

    except Exception as e:
        print(f"خطأ في إعادة تعيين كلمة المرور: {str(e)}")
        return jsonify({"error": f"خطأ في إعادة تعيين كلمة المرور: {str(e)}"}), 500

@app.route('/test_students_connection')
def test_students_connection():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('test_students_connection.html')

@app.route('/test_add_student')
def test_add_student():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('test_add_student.html')

@app.route('/student_registration')
def student_registration():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('student_registration.html')

# Grades Management Routes (تم نقل هذا إلى أسفل الملف)



# API للدرجات
@app.route('/api/grades', methods=['GET'])
def get_grades():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # يمكن إضافة فلاتر للطالب، المادة، العام الدراسي، الفصل
        student_id = request.args.get('student_id')
        subject_id = request.args.get('subject_id')
        academic_year_id = request.args.get('academic_year_id')
        semester_id = request.args.get('semester_id')

        query = """
            SELECT g.grade_id, g.student_id, g.subject_id, g.academic_year_id, g.semester_id,
                   g.instructor_id, g.midterm_grade, g.final_grade, g.coursework_grade,
                   g.total_grade, g.letter_grade, g.grade_points, g.status, g.notes,
                   g.created_date, s.full_name as student_name, s.student_number,
                   sub.subject_name, sub.subject_code, ay.year_name, sem.semester_name,
                   f.full_name as instructor_name
            FROM grades g
            LEFT JOIN students s ON g.student_id = s.student_id
            LEFT JOIN subjects sub ON g.subject_id = sub.subject_id
            LEFT JOIN academic_years ay ON g.academic_year_id = ay.academic_year_id
            LEFT JOIN semesters sem ON g.semester_id = sem.semester_id
            LEFT JOIN faculty f ON g.instructor_id = f.faculty_id
            WHERE 1=1
        """

        params = []
        if student_id:
            query += " AND g.student_id = ?"
            params.append(student_id)
        if subject_id:
            query += " AND g.subject_id = ?"
            params.append(subject_id)
        if academic_year_id:
            query += " AND g.academic_year_id = ?"
            params.append(academic_year_id)
        if semester_id:
            query += " AND g.semester_id = ?"
            params.append(semester_id)

        query += " ORDER BY g.created_date DESC"

        cursor.execute(query, params)

        grades = []
        for row in cursor.fetchall():
            grades.append({
                'grade_id': row.grade_id,
                'student_id': row.student_id,
                'subject_id': row.subject_id,
                'academic_year_id': row.academic_year_id,
                'semester_id': row.semester_id,
                'instructor_id': row.instructor_id,
                'midterm_grade': float(row.midterm_grade) if row.midterm_grade else None,
                'final_grade': float(row.final_grade) if row.final_grade else None,
                'coursework_grade': float(row.coursework_grade) if row.coursework_grade else None,
                'total_grade': float(row.total_grade) if row.total_grade else None,
                'letter_grade': row.letter_grade,
                'grade_points': float(row.grade_points) if row.grade_points else None,
                'status': row.status,
                'notes': row.notes,
                'student_name': row.student_name,
                'student_number': row.student_number,
                'subject_name': row.subject_name,
                'subject_code': row.subject_code,
                'year_name': row.year_name,
                'semester_name': row.semester_name,
                'instructor_name': row.instructor_name,
                'created_date': row.created_date.strftime('%Y-%m-%d') if row.created_date else None
            })

        conn.close()
        return jsonify(grades)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل البيانات: {str(e)}'}), 500

@app.route('/api/grades', methods=['POST'])
def add_grade():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        required_fields = ['student_id', 'subject_id', 'academic_year_id', 'semester_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من عدم وجود درجة مسبقة لنفس الطالب والمادة والفصل
        cursor.execute("""
            SELECT COUNT(*) FROM grades
            WHERE student_id = ? AND subject_id = ? AND academic_year_id = ? AND semester_id = ?
        """, (data.get('student_id'), data.get('subject_id'),
              data.get('academic_year_id'), data.get('semester_id')))

        if cursor.fetchone()[0] > 0:
            return jsonify({'error': 'درجة هذا الطالب في هذه المادة موجودة مسبقاً'}), 400

        # حساب الدرجة الإجمالية ونقاط الدرجة
        midterm = float(data.get('midterm_grade', 0))
        final = float(data.get('final_grade', 0))
        coursework = float(data.get('coursework_grade', 0))
        total_grade = midterm + final + coursework

        # تحديد الدرجة الحرفية ونقاط الدرجة
        letter_grade, grade_points = calculate_letter_grade(total_grade)

        cursor.execute("""
            INSERT INTO grades (student_id, subject_id, academic_year_id, semester_id, instructor_id,
                              midterm_grade, final_grade, coursework_grade, total_grade, letter_grade,
                              grade_points, status, notes, created_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETDATE())
        """, (data.get('student_id'), data.get('subject_id'), data.get('academic_year_id'),
              data.get('semester_id'), data.get('instructor_id'), midterm, final, coursework,
              total_grade, letter_grade, grade_points, data.get('status', 'مكتملة'), data.get('notes')))

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('إضافة درجة', 'INSERT', f"درجة طالب ID: {data.get('student_id')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم إضافة الدرجة بنجاح'}), 201

    except Exception as e:
        return jsonify({'error': f'خطأ في إضافة الدرجة: {str(e)}'}), 500

@app.route('/api/grades/<int:grade_id>', methods=['PUT'])
def update_grade(grade_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()

        required_fields = ['student_id', 'subject_id', 'academic_year_id', 'semester_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'الحقل {field} مطلوب'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # حساب الدرجة الإجمالية ونقاط الدرجة
        midterm = float(data.get('midterm_grade', 0))
        final = float(data.get('final_grade', 0))
        coursework = float(data.get('coursework_grade', 0))
        total_grade = midterm + final + coursework

        # تحديد الدرجة الحرفية ونقاط الدرجة
        letter_grade, grade_points = calculate_letter_grade(total_grade)

        cursor.execute("""
            UPDATE grades
            SET student_id = ?, subject_id = ?, academic_year_id = ?, semester_id = ?, instructor_id = ?,
                midterm_grade = ?, final_grade = ?, coursework_grade = ?, total_grade = ?,
                letter_grade = ?, grade_points = ?, status = ?, notes = ?, updated_date = GETDATE()
            WHERE grade_id = ?
        """, (data.get('student_id'), data.get('subject_id'), data.get('academic_year_id'),
              data.get('semester_id'), data.get('instructor_id'), midterm, final, coursework,
              total_grade, letter_grade, grade_points, data.get('status', 'مكتملة'),
              data.get('notes'), grade_id))

        if cursor.rowcount == 0:
            return jsonify({'error': 'الدرجة غير موجودة'}), 404

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('تحديث درجة', 'UPDATE', f"درجة ID: {grade_id}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم تحديث الدرجة بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في تحديث الدرجة: {str(e)}'}), 500

@app.route('/api/grades/<int:grade_id>', methods=['DELETE'])
def delete_grade(grade_id):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الدرجة
        cursor.execute("SELECT grade_id FROM grades WHERE grade_id = ?", (grade_id,))
        grade = cursor.fetchone()

        if not grade:
            return jsonify({'error': 'الدرجة غير موجودة'}), 404

        # حذف الدرجة
        cursor.execute("DELETE FROM grades WHERE grade_id = ?", (grade_id,))
        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('حذف درجة', 'DELETE', f"درجة ID: {grade_id}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم حذف الدرجة بنجاح'}), 200

    except Exception as e:
        return jsonify({'error': f'خطأ في حذف الدرجة: {str(e)}'}), 500

# دالة مساعدة لحساب الدرجة الحرفية
def calculate_letter_grade(total_grade):
    if total_grade >= 95:
        return 'A+', 4.00
    elif total_grade >= 90:
        return 'A', 3.75
    elif total_grade >= 85:
        return 'B+', 3.50
    elif total_grade >= 80:
        return 'B', 3.00
    elif total_grade >= 75:
        return 'C+', 2.50
    elif total_grade >= 70:
        return 'C', 2.00
    elif total_grade >= 65:
        return 'D+', 1.50
    elif total_grade >= 60:
        return 'D', 1.00
    else:
        return 'F', 0.00

# Reports Routes
@app.route('/grade_reports')
def grade_reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('grade_reports.html')

@app.route('/student_transcript')
def student_transcript():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('student_transcript.html')

@app.route('/subject_reports')
def subject_reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('subject_reports.html')

@app.route('/reports')
def reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('semester_reports'))

@app.route('/semester_reports')
def semester_reports():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('semester_reports.html')

@app.route('/api/semester_report', methods=['POST'])
def get_semester_report():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'لا توجد بيانات مرسلة'}), 400

        academic_year = data.get('academic_year', '')
        semester = data.get('semester', '')
        academic_program = data.get('academic_program', '')
        student_name = data.get('student_name', '')

        if not all([academic_year, semester, academic_program, student_name]):
            return jsonify({'error': 'جميع الحقول مطلوبة'}), 400

        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب بيانات الطالب
        cursor.execute("""
            SELECT student_id, name, program_id, year_id
            FROM students
            WHERE name = ?
        """, (student_name,))

        student_info = cursor.fetchone()
        if not student_info:
            conn.close()
            return jsonify({'error': 'لم يتم العثور على الطالب'}), 404

        # جلب معلومات الجامعة والكلية والقسم
        cursor.execute("""
            SELECT u.name_ar as university_name, c.name_ar as college_name,
                   d.name as department_name, ap.name as program_name,
                   u.university_id, c.college_id
            FROM students s
            INNER JOIN academic_programs ap ON s.program_id = ap.program_id
            INNER JOIN departments d ON ap.department_id = d.department_id
            INNER JOIN colleges c ON d.college_id = c.college_id
            INNER JOIN universities u ON c.university_id = u.university_id
            WHERE s.student_id = ?
        """, (student_info[0],))

        institution_info = cursor.fetchone()

        # استعلام لجلب الدرجات من جدول grades باستخدام حقل الدرجة كاملة فقط
        cursor.execute("""
            SELECT
                ISNULL(sub.name, N'مادة غير محددة') as subject_name,
                ISNULL(CAST(sub.units AS INT), 3) as credit_hours,
                0.0 as coursework_grade,
                0.0 as final_grade,
                ISNULL(CAST(g.total_grade AS FLOAT), 0.0) as total_grade,
                ISNULL(g.grade_assessment, N'-') as letter_grade
            FROM grades g
            INNER JOIN students s ON g.student_id = s.student_id
            INNER JOIN subjects sub ON g.subject_id = sub.subject_id
            INNER JOIN academic_years ay ON g.academic_year_id = ay.year_id
            INNER JOIN semesters sem ON g.semester_id = sem.semester_id
            INNER JOIN academic_programs ap ON g.academic_program_id = ap.program_id
            WHERE s.name = ?
                AND ay.name = ?
                AND sem.name = ?
                AND ap.name = ?
            ORDER BY sub.name
        """, (student_name, academic_year, semester, academic_program))

        grades_rows = cursor.fetchall()
        grades_data = []

        if grades_rows:
            for row in grades_rows:
                grades_data.append({
                    'subject_name': row[0],
                    'credit_hours': row[1] if row[1] else 3,  # افتراضي 3 وحدات إذا لم تكن محددة
                    'coursework_grade': float(row[2]) if row[2] else 0.0,
                    'final_grade': float(row[3]) if row[3] else 0.0,
                    'total_grade': float(row[4]) if row[4] else 0.0,
                    'letter_grade': row[5] if row[5] else '-'
                })
        else:
            # إذا لم توجد درجات، نحاول البحث في جدول research_subjects
            cursor.execute("""
                SELECT
                    ISNULL(subject_name, N'مادة غير محددة') as subject_name,
                    ISNULL(CAST(credit_hours AS INT), 3) as credit_hours,
                    0.0 as coursework_grade,
                    0.0 as final_grade,
                    ISNULL(CAST(grade AS FLOAT), 0.0) as total_grade,
                    ISNULL(letter_grade, N'-') as letter_grade
                FROM research_subjects
                WHERE student_name = ?
                ORDER BY subject_name
            """, (student_name,))

            research_grades = cursor.fetchall()

            if research_grades:
                for row in research_grades:
                    grades_data.append({
                        'subject_name': row[0],
                        'credit_hours': row[1] if row[1] else 3,
                        'coursework_grade': float(row[2]) if row[2] else 0.0,
                        'final_grade': float(row[3]) if row[3] else 0.0,
                        'total_grade': float(row[4]) if row[4] else 0.0,
                        'letter_grade': row[5] if row[5] else '-'
                    })
            else:
                # إذا لم توجد درجات في أي من الجدولين
                grades_data = [{
                    'subject_name': 'لا توجد درجات مسجلة لهذا الطالب في الفصل المحدد',
                    'credit_hours': 0,
                    'coursework_grade': 0.0,
                    'final_grade': 0.0,
                    'total_grade': 0.0,
                    'letter_grade': '-'
                }]

        # حساب المعدل بالطريقة الصحيحة (ضرب الدرجة × عدد الوحدات)
        total_points = 0
        total_credit_hours = 0

        for grade in grades_data:
            if grade['total_grade'] and grade['total_grade'] > 0:
                # تحويل الدرجة الرقمية إلى نقاط (من 4.0)
                # نظام التقدير: 90-100=4.0, 80-89=3.0, 70-79=2.0, 60-69=1.0, أقل من 60=0.0
                total_grade = grade['total_grade']
                if total_grade >= 90:
                    grade_points = 4.0
                elif total_grade >= 80:
                    grade_points = 3.0
                elif total_grade >= 70:
                    grade_points = 2.0
                elif total_grade >= 60:
                    grade_points = 1.0
                else:
                    grade_points = 0.0

                total_points += grade_points * grade['credit_hours']
                total_credit_hours += grade['credit_hours']

        gpa = round(total_points / total_credit_hours, 2) if total_credit_hours > 0 else 0.0

        response_data = {
            'student_info': {
                'student_id': student_info[0],
                'student_name': student_info[1],
                'department': institution_info[2] if institution_info else 'قسم إدارة الأعمال',
                'academic_program': academic_program,
                'academic_year': academic_year,
                'semester': semester,
                'university_name': institution_info[0] if institution_info else 'جامعة السودان للعلوم والتكنولوجيا',
                'college_name': institution_info[1] if institution_info else 'كلية الدراسات العليا',
                'university_id': institution_info[4] if institution_info else 1,
                'college_id': institution_info[5] if institution_info else 1
            },
            'grades': grades_data,
            'gpa': gpa
        }

        conn.close()
        return jsonify(response_data)

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"خطأ في API التقرير: {error_details}")
        return jsonify({'error': f'خطأ في جلب التقرير: {str(e)}'}), 500



@app.route('/api/semesters_by_year/<academic_year>')
def get_semesters_by_year(academic_year):
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب الفصول الدراسية للعام المحدد
        cursor.execute("""
            SELECT DISTINCT s.name as semester_name
            FROM semesters s
            INNER JOIN academic_years ay ON s.year_id = ay.year_id
            WHERE ay.name = ?
            ORDER BY s.name
        """, (academic_year,))

        semesters = [{'semester': row[0]} for row in cursor.fetchall()]

        conn.close()
        return jsonify(semesters)

    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الفصول الدراسية: {str(e)}'}), 500

@app.route('/api/students_by_program')
def get_students_for_grades():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401

    academic_year = request.args.get('academic_year')
    academic_program = request.args.get('academic_program')

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب الطلاب حسب العام الدراسي والبرنامج الأكاديمي
        cursor.execute("""
            SELECT DISTINCT s.name as student_name
            FROM students s
            INNER JOIN academic_years ay ON s.year_id = ay.year_id
            INNER JOIN academic_programs ap ON s.program_id = ap.program_id
            WHERE ay.name = ? AND ap.name = ?
            ORDER BY s.name
        """, (academic_year, academic_program))

        students = [{'student_name': row[0]} for row in cursor.fetchall()]

        conn.close()
        return jsonify(students)

    except Exception as e:
        return jsonify({'error': f'خطأ في جلب الطلاب: {str(e)}'}), 500

# User Management Routes
@app.route('/users')
def users():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    if session.get('user_type') != 'مدير النظام':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    return render_template('users.html')

# System Routes
@app.route('/settings')
def settings():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    if session.get('user_type') != 'مدير النظام':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    return render_template('settings.html')

@app.route('/backup')
def backup():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    if session.get('user_type') != 'مدير النظام':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    return render_template('backup.html')

@app.route('/system_log')
def system_log():
    if 'user_id' not in session:
        return redirect(url_for('login'))
    if session.get('user_type') != 'مدير النظام':
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('index'))
    return render_template('system_log.html')

# API للنسخ الاحتياطي
@app.route('/api/backup/create', methods=['POST'])
def create_backup():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    if session.get('user_type') != 'مدير النظام':
        return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403

    try:
        data = request.get_json()
        backup_name = data.get('backup_name', f'backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        include_system_log = data.get('include_system_log', True)

        conn = get_db_connection()
        cursor = conn.cursor()

        # إنشاء محتوى النسخة الاحتياطية بتنسيق SQL Server
        backup_content = f"-- نسخة احتياطية من نظام إدارة الدراسات العليا\n"
        backup_content += f"-- تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        backup_content += f"-- اسم النسخة: {backup_name}\n"
        backup_content += f"-- يمكن استعادة هذه النسخة باستخدام SQL Server Management Studio\n\n"

        # قائمة الجداول للنسخ الاحتياطي
        tables = [
            'universities', 'colleges', 'departments', 'specializations',
            'academic_programs', 'academic_years', 'semesters', 'admission_channels',
            'subjects', 'students', 'faculty', 'users', 'grades', 'research_subjects'
        ]

        if include_system_log:
            tables.append('system_log')

        for table in tables:
            try:
                # الحصول على هيكل الجدول
                cursor.execute(f"""
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = '{table}'
                    ORDER BY ORDINAL_POSITION
                """)
                columns = cursor.fetchall()

                if columns:
                    backup_content += f"-- جدول {table}\n"

                    # الحصول على البيانات
                    cursor.execute(f"SELECT * FROM {table}")
                    rows = cursor.fetchall()

                    if rows:
                        column_names = [col.COLUMN_NAME for col in columns]

                        # تفعيل IDENTITY_INSERT إذا كان الجدول يحتوي على عمود IDENTITY
                        backup_content += f"SET IDENTITY_INSERT {table} ON;\n"
                        backup_content += f"INSERT INTO {table} ({', '.join(column_names)}) VALUES\n"

                        values_list = []
                        for row in rows:
                            values = []
                            for i, value in enumerate(row):
                                if value is None:
                                    values.append('NULL')
                                elif isinstance(value, str):
                                    escaped_value = value.replace("'", "''")
                                    values.append(f"N'{escaped_value}'")
                                elif isinstance(value, bytes):
                                    # معالجة البيانات الثنائية (مثل الصور)
                                    hex_value = value.hex()
                                    values.append(f"0x{hex_value}")
                                elif hasattr(value, 'isoformat'):  # datetime object
                                    values.append(f"'{value.isoformat()}'")
                                else:
                                    values.append(str(value))
                            values_list.append(f"({', '.join(values)})")

                        backup_content += ',\n'.join(values_list) + ";\n"
                        backup_content += f"SET IDENTITY_INSERT {table} OFF;\n\n"
                    else:
                        backup_content += f"-- لا توجد بيانات في جدول {table}\n\n"

            except Exception as e:
                backup_content += f"-- خطأ في نسخ جدول {table}: {str(e)}\n\n"

        conn.close()

        # تسجيل العملية في سجل النظام
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
                VALUES (?, ?, ?, ?, GETDATE())
            """, ('إنشاء نسخة احتياطية', 'BACKUP', f"نسخة احتياطية: {backup_name}", session.get('username')))
            conn.commit()
            conn.close()
        except:
            pass  # تجاهل أخطاء تسجيل السجل

        # إرجاع الملف
        response = make_response(backup_content)
        response.headers['Content-Type'] = 'application/sql; charset=utf-8'
        # استخدام ASCII فقط في اسم الملف لتجنب مشاكل الترميز
        safe_filename = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.bak"
        response.headers['Content-Disposition'] = f'attachment; filename="{safe_filename}"'

        return response

    except Exception as e:
        return jsonify({'error': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'}), 500

@app.route('/api/backup/restore', methods=['POST'])
def restore_backup():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    if session.get('user_type') != 'مدير النظام':
        return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403

    try:
        import os
        import tempfile

        if 'backup_file' not in request.files:
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400

        file = request.files['backup_file']
        if file.filename == '':
            return jsonify({'error': 'لم يتم اختيار ملف'}), 400

        if not file.filename.lower().endswith('.bak'):
            return jsonify({'error': 'يجب أن يكون الملف من نوع .bak'}), 400

        # قراءة محتوى الملف
        backup_content = file.read().decode('utf-8')

        conn = get_db_connection()
        cursor = conn.cursor()

        # تنفيذ أوامر SQL من الملف
        # تقسيم المحتوى إلى أوامر منفصلة
        sql_commands = []
        current_command = ""

        for line in backup_content.split('\n'):
            line = line.strip()
            if line and not line.startswith('--'):
                current_command += line + " "
                if line.endswith(';'):
                    sql_commands.append(current_command.strip())
                    current_command = ""

        success_count = 0
        error_count = 0

        # قائمة الجداول التي تحتوي على IDENTITY columns
        identity_tables = [
            'universities', 'colleges', 'departments', 'specializations',
            'academic_programs', 'academic_years', 'semesters', 'admission_channels',
            'subjects', 'students', 'faculty', 'users', 'grades', 'research_subjects', 'system_log'
        ]

        for command in sql_commands:
            if command:
                try:
                    # البحث عن اسم الجدول في الأمر
                    table_name = None
                    for table in identity_tables:
                        if f"INSERT INTO {table}" in command:
                            table_name = table
                            break

                    # تفعيل IDENTITY_INSERT للجدول المحدد فقط
                    if table_name:
                        try:
                            cursor.execute(f"SET IDENTITY_INSERT {table_name} ON")
                        except Exception as e:
                            print(f"تحذير: لا يمكن تفعيل IDENTITY_INSERT للجدول {table_name}: {str(e)}")

                    # تنفيذ الأمر
                    cursor.execute(command)
                    success_count += 1

                    # إيقاف IDENTITY_INSERT للجدول المحدد
                    if table_name:
                        try:
                            cursor.execute(f"SET IDENTITY_INSERT {table_name} OFF")
                        except Exception as e:
                            print(f"تحذير: لا يمكن إيقاف IDENTITY_INSERT للجدول {table_name}: {str(e)}")

                except Exception as cmd_error:
                    error_count += 1
                    print(f"خطأ في تنفيذ الأمر: {command[:100]}... - {str(cmd_error)}")
                    continue

        conn.commit()
        conn.close()

        # تسجيل العملية في سجل النظام
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
                VALUES (?, ?, ?, ?, GETDATE())
            """, ('استعادة نسخة احتياطية', 'RESTORE', f"ملف: {file.filename} - نجح: {success_count}, فشل: {error_count}", session.get('username')))
            conn.commit()
            conn.close()
        except:
            pass  # تجاهل أخطاء تسجيل السجل

        message = f'تم استعادة النسخة الاحتياطية بنجاح. تم تنفيذ {success_count} أمر بنجاح'
        if error_count > 0:
            message += f' مع {error_count} خطأ'

        return jsonify({'message': message})

    except Exception as e:
        return jsonify({'error': f'خطأ في استعادة النسخة الاحتياطية: {str(e)}'}), 500

@app.route('/api/backup/clear_data', methods=['POST'])
def clear_data():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    if session.get('user_type') != 'مدير النظام':
        return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403

    try:
        data = request.get_json()
        clear_type = data.get('clear_type')

        conn = get_db_connection()
        cursor = conn.cursor()

        # معالجة الجداول الفردية
        if clear_type == 'universities':
            cursor.execute("DELETE FROM universities")
            target_object = "الجامعات"
        elif clear_type == 'colleges':
            cursor.execute("DELETE FROM colleges")
            target_object = "الكليات"
        elif clear_type == 'departments':
            cursor.execute("DELETE FROM departments")
            target_object = "الأقسام"
        elif clear_type == 'specializations':
            cursor.execute("DELETE FROM specializations")
            target_object = "التخصصات"
        elif clear_type == 'academic_programs':
            cursor.execute("DELETE FROM academic_programs")
            target_object = "البرامج الأكاديمية"
        elif clear_type == 'academic_years':
            cursor.execute("DELETE FROM academic_years")
            target_object = "الأعوام الدراسية"
        elif clear_type == 'semesters':
            cursor.execute("DELETE FROM semesters")
            target_object = "الفصول الدراسية"
        elif clear_type == 'subjects':
            cursor.execute("DELETE FROM subjects")
            target_object = "المواد الدراسية"
        elif clear_type == 'admission_channels':
            cursor.execute("DELETE FROM admission_channels")
            target_object = "قنوات القبول"
        elif clear_type == 'students':
            cursor.execute("DELETE FROM grades WHERE student_id IS NOT NULL")
            cursor.execute("DELETE FROM research_subjects WHERE student_name IS NOT NULL")
            cursor.execute("DELETE FROM students")
            target_object = "بيانات الطلبة والدرجات المرتبطة"
        elif clear_type == 'faculty':
            cursor.execute("DELETE FROM faculty")
            target_object = "بيانات أعضاء هيئة التدريس"
        elif clear_type == 'users':
            cursor.execute("DELETE FROM users WHERE user_type != 'مدير النظام'")
            target_object = "المستخدمين (عدا مدير النظام)"
        elif clear_type == 'grades':
            cursor.execute("DELETE FROM grades")
            target_object = "الدرجات"
        elif clear_type == 'research_subjects':
            cursor.execute("DELETE FROM research_subjects")
            target_object = "مواد السنة البحثية"
        elif clear_type == 'system_log':
            cursor.execute("DELETE FROM system_log")
            target_object = "سجل النظام"
        elif clear_type == 'multiple':
            # معالجة حذف جداول متعددة - سيتم تمرير قائمة الجداول
            tables = data.get('tables', [])
            target_object = f"جداول متعددة: {', '.join(tables)}"
            # حذف الجداول بالترتيب الصحيح
            table_order = [
                'research_subjects', 'grades', 'students', 'faculty', 'users',
                'subjects', 'academic_programs', 'specializations', 'departments',
                'colleges', 'universities', 'semesters', 'academic_years',
                'admission_channels', 'system_log'
            ]
            for table in table_order:
                if table in tables:
                    try:
                        cursor.execute(f"DELETE FROM {table}")
                    except:
                        pass
        elif clear_type == 'all':
            # حذف البيانات بالترتيب الصحيح لتجنب مشاكل المفاتيح الخارجية
            tables_to_clear = [
                'research_subjects', 'grades', 'students', 'faculty', 'subjects',
                'academic_programs', 'specializations', 'departments', 'colleges',
                'universities', 'semesters', 'academic_years', 'admission_channels'
            ]

            for table in tables_to_clear:
                try:
                    cursor.execute(f"DELETE FROM {table}")
                except:
                    pass  # تجاهل الأخطاء إذا كان الجدول غير موجود

            target_object = "جميع البيانات"
        else:
            return jsonify({'error': 'نوع المسح غير صحيح'}), 400

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('مسح البيانات', 'DELETE', target_object, session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': f'تم مسح {target_object} بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في مسح البيانات: {str(e)}'}), 500



# API لسجل النظام
@app.route('/api/system_log', methods=['GET'])
def get_system_log():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    if session.get('user_type') != 'مدير النظام':
        return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT log_id, operation_name, operation_type, target_object,
                   username, operation_date, created_date
            FROM system_log
            ORDER BY operation_date DESC
        """)

        logs = []
        for row in cursor.fetchall():
            log = {
                'log_id': row.log_id,
                'operation_name': row.operation_name,
                'operation_type': row.operation_type,
                'target_object': row.target_object,
                'username': row.username,
                'operation_date': row.operation_date.isoformat() if row.operation_date else None,
                'created_date': row.created_date.isoformat() if row.created_date else None
            }
            logs.append(log)

        conn.close()
        return jsonify(logs)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل سجل النظام: {str(e)}'}), 500

@app.route('/api/system_log/clear', methods=['DELETE'])
def clear_system_log():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    if session.get('user_type') != 'مدير النظام':
        return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # حذف جميع السجلات
        cursor.execute("DELETE FROM system_log")

        # تسجيل عملية مسح السجل
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, ('مسح سجل النظام', 'DELETE', 'جميع سجلات النظام', session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'message': 'تم مسح سجل النظام بنجاح'})

    except Exception as e:
        return jsonify({'error': f'خطأ في مسح سجل النظام: {str(e)}'}), 500

@app.route('/api/stats', methods=['GET'])
def get_system_stats():
    if 'user_id' not in session:
        return jsonify({'error': 'غير مصرح'}), 401
    if session.get('user_type') != 'مدير النظام':
        return jsonify({'error': 'ليس لديك صلاحية للوصول'}), 403

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        stats = {}

        # البيانات الأساسية
        try:
            cursor.execute("SELECT COUNT(*) FROM universities")
            stats['universities'] = cursor.fetchone()[0]
        except:
            stats['universities'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM colleges")
            stats['colleges'] = cursor.fetchone()[0]
        except:
            stats['colleges'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM departments")
            stats['departments'] = cursor.fetchone()[0]
        except:
            stats['departments'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM specializations")
            stats['specializations'] = cursor.fetchone()[0]
        except:
            stats['specializations'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM academic_programs")
            stats['academic_programs'] = cursor.fetchone()[0]
        except:
            stats['academic_programs'] = 0

        # البيانات الأكاديمية
        try:
            cursor.execute("SELECT COUNT(*) FROM academic_years")
            stats['academic_years'] = cursor.fetchone()[0]
        except:
            stats['academic_years'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM semesters")
            stats['semesters'] = cursor.fetchone()[0]
        except:
            stats['semesters'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM subjects")
            stats['subjects'] = cursor.fetchone()[0]
        except:
            stats['subjects'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM admission_channels")
            stats['admission_channels'] = cursor.fetchone()[0]
        except:
            stats['admission_channels'] = 0

        # بيانات الأشخاص
        try:
            cursor.execute("SELECT COUNT(*) FROM students")
            stats['students'] = cursor.fetchone()[0]
        except:
            stats['students'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM faculty")
            stats['faculty'] = cursor.fetchone()[0]
        except:
            stats['faculty'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM users")
            stats['users'] = cursor.fetchone()[0]
        except:
            stats['users'] = 0

        # البيانات الأكاديمية المتقدمة
        try:
            cursor.execute("SELECT COUNT(*) FROM grades")
            stats['grades'] = cursor.fetchone()[0]
        except:
            stats['grades'] = 0

        try:
            cursor.execute("SELECT COUNT(*) FROM research_subjects")
            stats['research_subjects'] = cursor.fetchone()[0]
        except:
            stats['research_subjects'] = 0

        # بيانات النظام
        try:
            cursor.execute("SELECT COUNT(*) FROM system_log")
            stats['system_log'] = cursor.fetchone()[0]
        except:
            stats['system_log'] = 0

        conn.close()
        return jsonify(stats)

    except Exception as e:
        return jsonify({'error': f'خطأ في تحميل الإحصائيات: {str(e)}'}), 500

# ===== صفحات الإعدادات وإدارة قاعدة البيانات =====

@app.route('/database-setup')
def database_setup():
    """صفحة إعداد قاعدة البيانات"""
    error_message = request.args.get('error')
    return render_template('database_setup.html', error_message=error_message)

@app.route('/settings')
def settings_page():
    """صفحة الإعدادات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))
    return render_template('settings.html')

# ===== API للإعدادات =====

@app.route('/api/settings/test-connection', methods=['GET', 'POST'])
def api_test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        if request.method == 'POST':
            # تحديث الإعدادات مؤقتاً للاختبار
            form_data = request.form.to_dict()
            temp_config = {
                'server': form_data.get('server', ''),
                'database': form_data.get('database', ''),
                'driver': form_data.get('driver', ''),
                'authentication': form_data.get('authentication', ''),
                'username': form_data.get('username', ''),
                'password': form_data.get('password', '')
            }

            # إنشاء مثيل مؤقت للاختبار
            from database_config import DatabaseConfig
            temp_db_config = DatabaseConfig()
            temp_db_config.config.update(temp_config)

            success, message = temp_db_config.test_connection()
        else:
            # اختبار الإعدادات الحالية
            success, message = db_config.test_connection()

        return jsonify({
            'success': success,
            'message': message,
            'error': message if not success else None
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في اختبار الاتصال: {str(e)}'
        }), 500

@app.route('/api/settings/save', methods=['POST'])
def api_save_settings():
    """حفظ إعدادات قاعدة البيانات"""
    try:
        form_data = request.form.to_dict()

        new_config = {
            'server': form_data.get('server', ''),
            'database': form_data.get('database', ''),
            'driver': form_data.get('driver', ''),
            'authentication': form_data.get('authentication', ''),
            'username': form_data.get('username', ''),
            'password': form_data.get('password', '')
        }

        success = db_config.save_config(new_config)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم حفظ الإعدادات بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في حفظ الإعدادات'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في حفظ الإعدادات: {str(e)}'
        }), 500

@app.route('/api/settings/create-database', methods=['POST'])
def api_create_database():
    """إنشاء قاعدة البيانات"""
    try:
        form_data = request.form.to_dict()

        # تحديث الإعدادات مؤقتاً
        temp_config = {
            'server': form_data.get('server', ''),
            'database': form_data.get('database', ''),
            'driver': form_data.get('driver', ''),
            'authentication': form_data.get('authentication', ''),
            'username': form_data.get('username', ''),
            'password': form_data.get('password', '')
        }

        from database_config import DatabaseConfig
        temp_db_config = DatabaseConfig()
        temp_db_config.config.update(temp_config)

        success, message = temp_db_config.create_database()

        return jsonify({
            'success': success,
            'message': message,
            'error': message if not success else None
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في إنشاء قاعدة البيانات: {str(e)}'
        }), 500

@app.route('/api/settings/system-info')
def api_system_info():
    """معلومات النظام"""
    try:
        info = db_config.get_system_info()
        return jsonify({
            'success': True,
            **info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في تحميل معلومات النظام: {str(e)}'
        }), 500

@app.route('/api/settings/optimize-database', methods=['POST'])
def api_optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        success, message = db_config.optimize_database()

        return jsonify({
            'success': success,
            'message': message,
            'error': message if not success else None
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في تحسين قاعدة البيانات: {str(e)}'
        }), 500

@app.route('/api/settings/export')
def api_export_settings():
    """تصدير الإعدادات"""
    try:
        import json
        from datetime import datetime

        export_data = {
            'export_date': datetime.now().isoformat(),
            'system_version': '1.0.0',
            'database_config': {
                'server': db_config.config['server'],
                'database': db_config.config['database'],
                'driver': db_config.config['driver'],
                'authentication': db_config.config['authentication']
                # لا نصدر كلمة المرور لأسباب أمنية
            }
        }

        response = make_response(json.dumps(export_data, ensure_ascii=False, indent=2))
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=settings_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'

        return response

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'خطأ في تصدير الإعدادات: {str(e)}'
        }), 500

# ===== API لإعداد قاعدة البيانات =====

@app.route('/api/database-setup/test-connection', methods=['POST'])
def api_database_setup_test():
    """اختبار الاتصال من صفحة الإعداد"""
    return api_test_connection()

@app.route('/api/database-setup/create-database', methods=['POST'])
def api_database_setup_create():
    """إنشاء قاعدة البيانات من صفحة الإعداد"""
    return api_create_database()

@app.route('/api/database-setup/save', methods=['POST'])
def api_database_setup_save():
    """حفظ إعدادات قاعدة البيانات من صفحة الإعداد"""
    return api_save_settings()



# صفحة إدخال الدرجات
@app.route('/grades_entry')
def grades_entry():
    """صفحة إدخال الدرجات"""
    if 'user_id' not in session:
        return redirect(url_for('login'))

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب الأعوام الدراسية
        cursor.execute("SELECT year_id, name FROM academic_years ORDER BY name DESC")
        academic_years = [{'year_id': row[0], 'year_name': row[1]} for row in cursor.fetchall()]

        # جلب الفصول الدراسية
        cursor.execute("SELECT semester_id, name FROM semesters ORDER BY name")
        semesters = [{'semester_id': row[0], 'semester_name': row[1]} for row in cursor.fetchall()]

        # جلب البرامج الأكاديمية
        cursor.execute("SELECT program_id, name FROM academic_programs ORDER BY name")
        academic_programs = [{'program_id': row[0], 'program_name': row[1]} for row in cursor.fetchall()]

        # جلب المواد
        cursor.execute("SELECT subject_id, name FROM subjects ORDER BY name")
        subjects = [{'subject_id': row[0], 'subject_name': row[1]} for row in cursor.fetchall()]

        conn.close()

        return render_template('grades_entry_clean.html',
                             academic_years=academic_years,
                             semesters=semesters,
                             academic_programs=academic_programs,
                             subjects=subjects)

    except Exception as e:
        return f"خطأ في تحميل صفحة إدخال الدرجات: {str(e)}", 500

# API لجلب الطلبة حسب الفلاتر
@app.route('/api/grades/students', methods=['POST'])
def api_grades_students():
    """جلب الطلبة حسب الفلاتر المحددة"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح - يرجى تسجيل الدخول'})

    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات مرسلة'})

        academic_year_id = data.get('academic_year')
        semester_id = data.get('semester')
        academic_program_id = data.get('academic_program')
        subject_id = data.get('subject')

        # التحقق من وجود جميع المعايير المطلوبة
        if not all([academic_year_id, semester_id, academic_program_id, subject_id]):
            return jsonify({'success': False, 'error': 'جميع المعايير مطلوبة'})

        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب الطلبة المسجلين في البرنامج والعام الدراسي مع درجاتهم الحالية
        query = """
        SELECT DISTINCT
            s.student_id,
            s.name as student_name,
            CAST(s.student_id as NVARCHAR) as student_number,
            g.coursework_grade,
            g.final_exam_grade,
            g.total_grade,
            g.grade_letter
        FROM students s
        LEFT JOIN grades g ON s.student_id = g.student_id
            AND g.academic_year_id = ?
            AND g.semester_id = ?
            AND g.subject_id = ?
            AND g.academic_program_id = ?
        WHERE s.program_id = ?
        AND s.year_id = ?
        ORDER BY s.name
        """

        cursor.execute(query, (academic_year_id, semester_id, subject_id, academic_program_id, academic_program_id, academic_year_id))
        rows = cursor.fetchall()

        students = []
        for row in rows:
            students.append({
                'student_id': row[0],
                'student_name': row[1],
                'student_number': row[2],
                'coursework_grade': row[3],
                'final_exam_grade': row[4],
                'total_grade': row[5],
                'letter_grade': row[6]
            })

        conn.close()

        return jsonify({
            'success': True,
            'students': students
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API لحفظ درجة طالب واحد
@app.route('/api/grades/save', methods=['POST'])
def api_grades_save():
    """حفظ درجة طالب واحد"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # التحقق من وجود الدرجة مسبقاً
        cursor.execute("""
            SELECT grade_id FROM grades
            WHERE student_id = ? AND subject_id = ? AND academic_year_id = ? AND semester_id = ? AND academic_program_id = ?
        """, (data['student_id'], data['subject_id'], data['academic_year_id'], data['semester_id'], data['academic_program_id']))

        existing_grade = cursor.fetchone()

        if existing_grade:
            # تحديث الدرجة الموجودة
            cursor.execute("""
                UPDATE grades
                SET coursework_grade = ?, final_exam_grade = ?, total_grade = ?, grade_letter = ?, updated_date = GETDATE()
                WHERE grade_id = ?
            """, (data['coursework_grade'], data['final_exam_grade'], data['total_grade'], data['grade_letter'], existing_grade[0]))
        else:
            # إدراج درجة جديدة
            cursor.execute("""
                INSERT INTO grades (academic_year_id, semester_id, academic_program_id, subject_id, student_id, coursework_grade, final_exam_grade, total_grade, grade_letter)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (data['academic_year_id'], data['semester_id'], data['academic_program_id'],
                  data['subject_id'], data['student_id'], data['coursework_grade'], data['final_exam_grade'], data['total_grade'], data['grade_letter']))

        conn.commit()
        conn.close()

        # تسجيل العملية في سجل النظام (تم تعطيله مؤقتاً)
        # log_system_operation(
        #     operation_name="حفظ درجة",
        #     operation_type="إدراج/تحديث",
        #     target_object=f"الطالب {data['student_id']} - المادة {data['subject_id']}",
        #     username=session.get('username', 'غير معروف')
        # )

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API لحفظ جميع الدرجات
@app.route('/api/grades/save-all', methods=['POST'])
def api_grades_save_all():
    """حفظ جميع الدرجات"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        data = request.get_json()
        grades = data.get('grades', [])

        conn = get_db_connection()
        cursor = conn.cursor()

        saved_count = 0

        for grade in grades:
            # التحقق من وجود الدرجة مسبقاً
            cursor.execute("""
                SELECT grade_id FROM grades
                WHERE student_id = ? AND subject_id = ? AND academic_year_id = ? AND semester_id = ? AND academic_program_id = ?
            """, (grade['student_id'], grade['subject_id'], grade['academic_year_id'], grade['semester_id'], grade['academic_program_id']))

            existing_grade = cursor.fetchone()

            if existing_grade:
                # تحديث الدرجة الموجودة
                cursor.execute("""
                    UPDATE grades
                    SET coursework_grade = ?, final_exam_grade = ?, total_grade = ?, grade_letter = ?, updated_date = GETDATE()
                    WHERE grade_id = ?
                """, (grade['coursework_grade'], grade['final_exam_grade'], grade['total_grade'], grade['grade_letter'], existing_grade[0]))
            else:
                # إدراج درجة جديدة
                cursor.execute("""
                    INSERT INTO grades (academic_year_id, semester_id, academic_program_id, subject_id, student_id, coursework_grade, final_exam_grade, total_grade, grade_letter)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (grade['academic_year_id'], grade['semester_id'], grade['academic_program_id'],
                      grade['subject_id'], grade['student_id'], grade['coursework_grade'], grade['final_exam_grade'], grade['total_grade'], grade['grade_letter']))

            saved_count += 1

        conn.commit()
        conn.close()

        # تسجيل العملية في سجل النظام (تم تعطيله مؤقتاً)
        # log_system_operation(
        #     operation_name="حفظ درجات مجمع",
        #     operation_type="إدراج/تحديث",
        #     target_object=f"{saved_count} درجة",
        #     username=session.get('username', 'غير معروف')
        # )

        return jsonify({
            'success': True,
            'saved_count': saved_count
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API لجلب الفصول الدراسية حسب العام
@app.route('/api/semesters/by-year', methods=['POST'])
def api_semesters_by_year():
    """جلب الفصول الدراسية حسب العام الدراسي"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        data = request.get_json()
        academic_year_id = data.get('academic_year_id')

        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب الفصول المرتبطة بالعام الدراسي
        cursor.execute("""
            SELECT semester_id, name
            FROM semesters
            WHERE year_id = ?
            ORDER BY name
        """, (academic_year_id,))

        rows = cursor.fetchall()
        semesters = []
        for row in rows:
            semesters.append({
                'semester_id': row[0],
                'semester_name': row[1]
            })

        conn.close()

        return jsonify({
            'success': True,
            'semesters': semesters
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API لجلب المواد حسب البرنامج الأكاديمي
@app.route('/api/subjects/by-program', methods=['POST'])
def api_subjects_by_program():
    """جلب المواد حسب البرنامج الأكاديمي"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        data = request.get_json()
        program_id = data.get('program_id')

        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب المواد المرتبطة بالبرنامج الأكاديمي
        cursor.execute("""
            SELECT subject_id, name
            FROM subjects
            WHERE program_id = ?
            ORDER BY name
        """, (program_id,))

        rows = cursor.fetchall()
        subjects = []
        for row in rows:
            subjects.append({
                'subject_id': row[0],
                'subject_name': row[1]
            })

        conn.close()

        return jsonify({
            'success': True,
            'subjects': subjects
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# API لجلب المواد حسب البرنامج الأكاديمي والعام الدراسي
@app.route('/api/subjects/by-program-year', methods=['POST'])
def api_subjects_by_program_year():
    """جلب المواد حسب البرنامج الأكاديمي والعام الدراسي"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        data = request.get_json()
        program_id = data.get('program_id')
        academic_year_id = data.get('academic_year_id')

        conn = get_db_connection()
        cursor = conn.cursor()

        # جلب المواد المرتبطة بالبرنامج الأكاديمي والعام الدراسي
        cursor.execute("""
            SELECT subject_id, name
            FROM subjects
            WHERE program_id = ?
            AND (academic_year = ? OR academic_year IS NULL)
            ORDER BY name
        """, (program_id, academic_year_id))

        rows = cursor.fetchall()
        subjects = []
        for row in rows:
            subjects.append({
                'subject_id': row[0],
                'subject_name': row[1]
            })

        conn.close()

        return jsonify({
            'success': True,
            'subjects': subjects
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

# APIs لمواد السنة البحثية
@app.route('/api/research_subjects', methods=['GET'])
def get_research_subjects():
    """جلب جميع مواد السنة البحثية"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT rs.id, rs.academic_year_id, rs.academic_program_id, rs.subject_name,
                   rs.credit_hours, rs.student_name, rs.grade, rs.letter_grade,
                   ay.name as academic_year_name, ap.name as program_name
            FROM research_subjects rs
            LEFT JOIN academic_years ay ON rs.academic_year_id = ay.year_id
            LEFT JOIN academic_programs ap ON rs.academic_program_id = ap.program_id
            ORDER BY rs.created_date DESC
        """)

        subjects = []
        for row in cursor.fetchall():
            subjects.append({
                'id': row.id,
                'academic_year_id': row.academic_year_id,
                'academic_program_id': row.academic_program_id,
                'subject_name': row.subject_name,
                'credit_hours': row.credit_hours,
                'student_name': row.student_name,
                'grade': float(row.grade) if row.grade else None,
                'letter_grade': row.letter_grade,
                'academic_year_name': row.academic_year_name,
                'program_name': row.program_name
            })

        conn.close()
        return jsonify({'success': True, 'subjects': subjects})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/research_subjects', methods=['POST'])
def add_research_subject():
    """إضافة مادة سنة بحثية جديدة"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        data = request.get_json()

        conn = get_db_connection()
        cursor = conn.cursor()

        # حساب التقدير
        grade = float(data.get('grade', 0))
        if grade >= 90:
            letter_grade = 'امتياز'
        elif grade >= 80:
            letter_grade = 'جيد جداً'
        elif grade >= 70:
            letter_grade = 'جيد'
        elif grade >= 60:
            letter_grade = 'مقبول'
        else:
            letter_grade = 'راسب'

        # التحقق من وجود درجة سابقة
        cursor.execute("""
            SELECT id, grade FROM research_subjects
            WHERE academic_year_id = ? AND academic_program_id = ?
                  AND subject_name = ? AND student_name = ?
        """, (data.get('academic_year_id'), data.get('academic_program_id'),
              data.get('subject_name'), data.get('student_name')))

        existing_record = cursor.fetchone()

        if existing_record:
            # التحقق من أن الدرجة الجديدة مختلفة عن الموجودة
            if existing_record.grade is not None and float(existing_record.grade) == grade:
                conn.close()
                return jsonify({
                    'success': False,
                    'error': f'الطالب {data.get("student_name")} مسجل مسبقاً في مادة {data.get("subject_name")} بنفس الدرجة ({grade})'
                })

            # تحديث الدرجة الموجودة
            cursor.execute("""
                UPDATE research_subjects
                SET grade = ?, letter_grade = ?, credit_hours = ?, updated_date = GETDATE()
                WHERE id = ?
            """, (grade, letter_grade, data.get('credit_hours', 0), existing_record.id))

            operation_name = 'تحديث درجة مادة سنة بحثية'
            operation_type = 'UPDATE'
            message = f'تم تحديث درجة الطالب {data.get("student_name")} في مادة {data.get("subject_name")} بنجاح'
        else:
            # إدراج درجة جديدة
            cursor.execute("""
                INSERT INTO research_subjects (academic_year_id, academic_program_id, subject_name,
                                             credit_hours, student_name, grade, letter_grade)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (data.get('academic_year_id'), data.get('academic_program_id'),
                  data.get('subject_name'), data.get('credit_hours', 0),
                  data.get('student_name'), grade, letter_grade))

            operation_name = 'إضافة درجة مادة سنة بحثية'
            operation_type = 'INSERT'
            message = f'تم حفظ درجة الطالب {data.get("student_name")} في مادة {data.get("subject_name")} بنجاح'

        conn.commit()

        # تسجيل العملية في سجل النظام
        cursor.execute("""
            INSERT INTO system_log (operation_name, operation_type, target_object, username, operation_date)
            VALUES (?, ?, ?, ?, GETDATE())
        """, (operation_name, operation_type, f"مادة: {data.get('subject_name')} - طالب: {data.get('student_name')}", session.get('username')))

        conn.commit()
        conn.close()

        return jsonify({'success': True, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/research_subjects/grade', methods=['GET'])
def get_research_subject_grade():
    """جلب درجة طالب في مادة سنة بحثية معينة"""
    if 'user_id' not in session:
        return jsonify({'success': False, 'error': 'غير مصرح'})

    try:
        academic_year_id = request.args.get('academic_year_id')
        academic_program_id = request.args.get('academic_program_id')
        subject_name = request.args.get('subject_name')
        student_name = request.args.get('student_name')

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT grade, letter_grade, credit_hours
            FROM research_subjects
            WHERE academic_year_id = ? AND academic_program_id = ?
                  AND subject_name = ? AND student_name = ?
        """, (academic_year_id, academic_program_id, subject_name, student_name))

        result = cursor.fetchone()
        conn.close()

        if result:
            return jsonify({
                'success': True,
                'grade': float(result.grade) if result.grade else None,
                'letter_grade': result.letter_grade,
                'credit_hours': result.credit_hours
            })
        else:
            return jsonify({'success': True, 'grade': None})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
