{% extends "base.html" %}

{% block title %}نتيجة الفصل الدراسي الأول والثاني{% endblock %}

{% block content %}
<style>
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800;900&display=swap');

* {
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تنسيق الطباعة */
@media print {
    /* إزالة الرؤوس والتذييلات */
    @page {
        margin: 0;
        size: A4;
    }

    body {
        margin: 0;
        padding: 0;
        font-size: 12pt;
        line-height: 1.4;
    }

    .report-container {
        padding: 15mm;
        padding-top: 25mm;
        margin: 0;
        box-shadow: none;
        page-break-inside: avoid;
    }

    .logo-section {
        min-height: 120px;
        padding: 10px 0;
        margin-bottom: 10px;
    }

    .logo-container img {
        width: 100px !important;
        height: 100px !important;
    }

    .university-name {
        font-size: 18pt !important;
        margin-bottom: 2px !important;
    }

    .college-name {
        font-size: 14pt !important;
        margin-bottom: 2px !important;
    }

    .department-name {
        font-size: 12pt !important;
        margin-bottom: 4px !important;
    }

    .info-row-inline {
        padding: 8px !important;
        margin-bottom: 8px !important;
        background: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .grades-table {
        margin: 10px 0 !important;
        font-size: 10pt;
    }

    .grades-table th {
        background: #e0e0e0 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
        padding: 6px !important;
        font-size: 10pt !important;
    }

    .grades-table td {
        padding: 4px !important;
        font-size: 10pt !important;
    }

    .gpa-section {
        padding: 10px !important;
        margin-bottom: 15px !important;
        background: #f5f5f5 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .signature-section {
        margin-top: 20px !important;
        page-break-inside: avoid;
    }

    /* إخفاء عناصر غير ضرورية للطباعة */
    .btn, .card-header, .form-control, .form-select {
        display: none !important;
    }
}

/* تنسيق السهم القابل للطي */
.transition-all {
    transition: transform 0.3s ease;
}

.card-header[aria-expanded="false"] #toggleIcon {
    transform: rotate(-90deg);
}

.card-header:hover {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%) !important;
}
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white" style="cursor: pointer;" data-bs-toggle="collapse" data-bs-target="#reportsCollapse" aria-expanded="true" aria-controls="reportsCollapse">
                    <h4 class="mb-0 d-flex justify-content-between align-items-center">
                        <span>
                            <i class="fas fa-file-alt me-2"></i>
                            نتيجة الفصل الدراسي الأول والثاني
                        </span>
                        <i class="fas fa-chevron-down transition-all" id="toggleIcon"></i>
                    </h4>
                </div>
                <div class="collapse show" id="reportsCollapse">
                    <div class="card-body">
                    <form id="reportForm" class="row g-2">
                        <div class="col-md-3">
                            <label for="academic_year" class="form-label">العام الدراسي</label>
                            <select class="form-select form-select-sm" id="academic_year" name="academic_year" required>
                                <option value="">اختر العام الدراسي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="semester" class="form-label">الفصل الدراسي</label>
                            <select class="form-select form-select-sm" id="semester" name="semester" required>
                                <option value="">اختر الفصل الدراسي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="academic_program" class="form-label">البرنامج الأكاديمي</label>
                            <select class="form-select form-select-sm" id="academic_program" name="academic_program" required>
                                <option value="">اختر البرنامج الأكاديمي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="student_name" class="form-label">اسم الطالب</label>
                            <select class="form-select form-select-sm" id="student_name" name="student_name" required>
                                <option value="">اختر اسم الطالب</option>
                            </select>
                        </div>
                        <div class="col-12 mt-3">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-print me-2"></i>
                                عرض التقرير
                            </button>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة التقرير -->
<div class="modal fade" id="reportModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">نتيجة الفصل الدراسي الأول والثاني</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="reportContent" class="report-container">
                    <!-- محتوى التقرير سيتم إدراجه هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<style>
/* تصميم التقرير */
.report-container {
    background: white;
    padding: 40px;
    padding-top: 60px;
    font-family: 'Arial', sans-serif;
    color: #333;
    line-height: 1.6;
}

.report-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 3px solid #2c3e50;
    padding-bottom: 15px;
}

.logo-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 15px 0;
    min-height: 200px;
}

.logo-container {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.logo-placeholder {
    width: 80px;
    height: 80px;
    border: 2px dashed #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    text-align: center;
}

.logo-placeholder {
    width: 80px;
    height: 80px;
    border: 2px solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #666;
    text-align: center;
}

.header-text {
    flex: 1;
    text-align: center;
}

.university-name {
    font-size: 28px;
    font-weight: 900;
    color: #1a365d;
    margin-bottom: 4px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.college-name {
    font-size: 22px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.department-name {
    font-size: 18px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 6px;
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.report-title {
    font-size: 20px;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 0;
}

.student-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.info-row {
    display: flex;
    margin-bottom: 10px;
}

.info-row-inline {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    text-align: center;
    padding: 10px;
}

.info-label {
    font-weight: 600;
    font-size: 14px;
    color: #ffffff;
    margin-bottom: 4px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.info-value {
    font-size: 16px;
    font-weight: 600;
    color: #f7fafc;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.grades-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.grades-table th,
.grades-table td {
    padding: 15px;
    text-align: center;
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.grades-table th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    font-weight: 700;
    font-size: 18px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.grades-table td {
    font-size: 16px;
    font-weight: 500;
    border-bottom: 1px solid #e2e8f0;
}

.grades-table tr:nth-child(even) {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.grades-table tr:nth-child(odd) {
    background-color: #ffffff;
}

.grades-table tr:hover {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    transform: scale(1.01);
    transition: all 0.3s ease;
}

.gpa-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 12px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.gpa-label {
    font-size: 20px;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin-bottom: 6px;
}

.gpa-note {
    color: #f7fafc !important;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.signature-section {
    margin-top: 35px;
    text-align: left;
    padding: 15px 0;
}

.signature-container {
    display: inline-block;
}

.signature-line {
    border-bottom: none;
    width: 250px;
    margin-bottom: 10px;
}

.signature-info {
    text-align: center;
}

.signature-name {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.signature-title {
    font-size: 14px;
    font-weight: 500;
    color: #4a5568;
    font-family: 'Noto Sans Arabic', 'SST Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تصميم الطباعة */
@media print {
    .modal-header,
    .modal-footer {
        display: none !important;
    }
    
    .modal-dialog {
        max-width: 100% !important;
        margin: 0 !important;
    }
    
    .modal-content {
        border: none !important;
        box-shadow: none !important;
    }
    
    .report-container {
        padding: 20px !important;
    }
    
    body * {
        visibility: hidden;
    }
    
    #reportModal,
    #reportModal * {
        visibility: visible;
    }
    
    #reportModal {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
let studentsData = [];
let programsData = [];
let academicYearsData = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAcademicYears();
    loadPrograms();

    // إضافة مستمعات للفلترة
    document.getElementById('academic_year').addEventListener('change', loadSemesters);
    document.getElementById('academic_program').addEventListener('change', loadStudentsByProgram);

    // إضافة مستمع لتحديث السهم عند الطي/الفتح
    const collapseElement = document.getElementById('reportsCollapse');
    const toggleIcon = document.getElementById('toggleIcon');

    collapseElement.addEventListener('show.bs.collapse', function () {
        toggleIcon.style.transform = 'rotate(0deg)';
    });

    collapseElement.addEventListener('hide.bs.collapse', function () {
        toggleIcon.style.transform = 'rotate(-90deg)';
    });
});

// تحميل قائمة الأعوام الدراسية
function loadAcademicYears() {
    fetch('/api/academic_years')
        .then(response => response.json())
        .then(data => {
            academicYearsData = data;
            const select = document.getElementById('academic_year');
            select.innerHTML = '<option value="">اختر العام الدراسي</option>';

            data.forEach(year => {
                const option = document.createElement('option');
                option.value = year.name;
                option.textContent = year.name;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الأعوام الدراسية:', error);
        });
}

// تحميل الفصول الدراسية حسب العام
function loadSemesters() {
    const academicYear = document.getElementById('academic_year').value;
    const semesterSelect = document.getElementById('semester');

    if (!academicYear) {
        semesterSelect.innerHTML = '<option value="">اختر الفصل الدراسي</option>';
        return;
    }

    fetch(`/api/semesters_by_year/${academicYear}`)
        .then(response => response.json())
        .then(data => {
            semesterSelect.innerHTML = '<option value="">اختر الفصل الدراسي</option>';

            data.forEach(semester => {
                const option = document.createElement('option');
                option.value = semester.semester;
                option.textContent = semester.semester;
                semesterSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الفصول الدراسية:', error);
        });
}

// تحميل قائمة البرامج الأكاديمية
function loadPrograms() {
    fetch('/api/academic_programs')
        .then(response => response.json())
        .then(data => {
            programsData = data;
            const select = document.getElementById('academic_program');
            select.innerHTML = '<option value="">اختر البرنامج الأكاديمي</option>';

            data.forEach(program => {
                const option = document.createElement('option');
                option.value = program.name;
                option.textContent = program.name;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل البرامج:', error);
        });
}

// تحميل الطلاب حسب البرنامج الأكاديمي والعام الدراسي
function loadStudentsByProgram() {
    const academicYear = document.getElementById('academic_year').value;
    const academicProgram = document.getElementById('academic_program').value;
    const studentSelect = document.getElementById('student_name');

    if (!academicYear || !academicProgram) {
        studentSelect.innerHTML = '<option value="">اختر اسم الطالب</option>';
        return;
    }

    fetch(`/api/students_by_program?academic_year=${academicYear}&academic_program=${academicProgram}`)
        .then(response => response.json())
        .then(data => {
            studentSelect.innerHTML = '<option value="">اختر اسم الطالب</option>';

            data.forEach(student => {
                const option = document.createElement('option');
                option.value = student.student_name;
                option.textContent = student.student_name;
                studentSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الطلاب:', error);
        });
}

// معالجة إرسال النموذج
document.getElementById('reportForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = {
        academic_year: formData.get('academic_year'),
        semester: formData.get('semester'),
        academic_program: formData.get('academic_program'),
        student_name: formData.get('student_name')
    };

    // جلب التقرير
    fetch('/api/semester_report', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
        } else {
            generateReport(data);
        }
    })
    .catch(error => {
        console.error('خطأ في جلب التقرير:', error);
        alert('حدث خطأ في جلب التقرير');
    });
});

// إنشاء التقرير
function generateReport(data) {
    const reportContent = document.getElementById('reportContent');
    
    let gradesRows = '';
    let sequence = 1;
    
    data.grades.forEach(grade => {
        gradesRows += `
            <tr>
                <td>${sequence++}</td>
                <td>${grade.subject_name}</td>
                <td>${grade.credit_hours || 3}</td>
                <td>${grade.total_grade || '-'}</td>
                <td>${grade.letter_grade || '-'}</td>
            </tr>
        `;
    });
    
    reportContent.innerHTML = `
        <div class="report-header">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="/api/universities/${data.student_info.university_id}/logo"
                         alt="شعار الجامعة"
                         style="width: 160px; height: 160px; object-fit: contain; border-radius: 20px; box-shadow: 0 8px 20px rgba(0,0,0,0.2);"
                         onerror="this.style.display='none';">
                </div>
                <div class="header-text">
                    <div class="university-name">${data.student_info.university_name || 'اسم الجامعة'}</div>
                    <div class="college-name">${data.student_info.college_name || 'اسم الكلية'}</div>
                    <div class="department-name">${data.student_info.department || 'اسم القسم'}</div>
                    <div class="report-title">نتيجة ${data.student_info.semester} - ${data.student_info.academic_year}</div>
                </div>
                <div class="logo-container">
                    <img src="/api/colleges/${data.student_info.college_id}/logo"
                         alt="شعار الكلية"
                         style="width: 160px; height: 160px; object-fit: contain; border-radius: 20px; box-shadow: 0 8px 20px rgba(0,0,0,0.2);"
                         onerror="this.style.display='none';">
                </div>
            </div>
        </div>
        
        <div class="student-info">
            <div class="info-row-inline">
                <span class="info-item">
                    <span class="info-label">معرف الطالب:</span>
                    <span class="info-value">${data.student_info.student_id}</span>
                </span>
                <span class="info-item">
                    <span class="info-label">اسم الطالب:</span>
                    <span class="info-value">${data.student_info.student_name}</span>
                </span>
                <span class="info-item">
                    <span class="info-label">البرنامج الأكاديمي:</span>
                    <span class="info-value">${data.student_info.academic_program}</span>
                </span>
            </div>
        </div>
        
        <table class="grades-table">
            <thead>
                <tr>
                    <th>التسلسل</th>
                    <th>اسم المادة</th>
                    <th>عدد الوحدات</th>
                    <th>الدرجة</th>
                    <th>التقدير</th>
                </tr>
            </thead>
            <tbody>
                ${gradesRows}
            </tbody>
        </table>
        
        <div class="gpa-section">
            <div class="gpa-label">معدل ${data.student_info.semester}: ${data.gpa}</div>
            <div class="gpa-note" style="font-size: 12px; color: #666; margin-top: 5px;">
                * طريقة حساب المعدل: (مجموع (درجة المادة × عدد وحداتها)) ÷ مجموع الوحدات
            </div>
        </div>

        <div class="signature-section">
            <div class="signature-container">
                <div class="signature-line"></div>
                <div class="signature-info">
                    <div class="signature-name">${data.student_info.department_head || 'رئيس القسم'}</div>
                    <div class="signature-title">رئيس ${data.student_info.department || 'القسم'}</div>
                </div>
            </div>
        </div>


    `;

    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById('reportModal'));
    modal.show();
}

// إضافة وظيفة طباعة نظيفة
function printReport() {
    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');
    const reportContent = document.getElementById('reportContent').innerHTML;

    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>تقرير الدرجات</title>
            <style>
                @page {
                    margin: 0;
                    size: A4;
                }

                body {
                    margin: 0;
                    padding: 20mm;
                    font-family: 'Noto Sans Arabic', 'SST Arabic', Arial, sans-serif;
                    font-size: 12pt;
                    line-height: 1.4;
                    direction: rtl;
                }

                .report-container {
                    background: white;
                    padding: 0;
                    color: #333;
                    line-height: 1.6;
                }

                .logo-section {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                    padding: 10px 0;
                    min-height: 120px;
                }

                .university-name {
                    font-size: 18pt;
                    font-weight: 900;
                    color: #1a365d;
                    margin-bottom: 2px;
                }

                .college-name {
                    font-size: 14pt;
                    font-weight: 700;
                    color: #2d3748;
                    margin-bottom: 2px;
                }

                .department-name {
                    font-size: 12pt;
                    font-weight: 600;
                    color: #4a5568;
                    margin-bottom: 4px;
                }

                .info-row-inline {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 8px;
                    padding: 8px;
                    background: #ffffff;
                    border: 2px solid #000000;
                    border-radius: 8px;
                }

                .info-label {
                    font-weight: 700;
                    font-size: 12pt;
                    color: #000000;
                    margin-bottom: 2px;
                }

                .info-value {
                    font-size: 14pt;
                    font-weight: 600;
                    color: #000000;
                }

                .grades-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 10px 0;
                    font-size: 10pt;
                }

                .grades-table th {
                    background: #ffffff;
                    padding: 8px;
                    font-size: 11pt;
                    font-weight: 700;
                    border: 2px solid #000000;
                    color: #000000;
                    text-align: center;
                }

                .grades-table td {
                    padding: 6px;
                    font-size: 11pt;
                    font-weight: 500;
                    border: 1px solid #000000;
                    text-align: center;
                    color: #000000;
                    background: #ffffff;
                }

                .gpa-section {
                    padding: 12px;
                    margin-bottom: 15px;
                    background: #ffffff;
                    border: 2px solid #000000;
                    border-radius: 8px;
                    text-align: center;
                }

                .gpa-label {
                    font-size: 14pt;
                    font-weight: 700;
                    color: #000000;
                    margin-bottom: 6px;
                }

                .gpa-value {
                    font-size: 18pt;
                    font-weight: 900;
                    color: #000000;
                }

                .signature-section {
                    margin-top: 40px;
                    text-align: left;
                    padding-top: 20px;
                }

                .signature-name {
                    font-size: 14pt;
                    font-weight: 700;
                    color: #000000;
                    margin-bottom: 5px;
                }

                .signature-title {
                    font-size: 12pt;
                    font-weight: 500;
                    color: #000000;
                }
            </style>
        </head>
        <body>
            ${reportContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // انتظار تحميل المحتوى ثم الطباعة
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

// استبدال وظيفة الطباعة القديمة بالجديدة
// الوظيفة الجديدة موجودة أعلاه
</script>
{% endblock %}
